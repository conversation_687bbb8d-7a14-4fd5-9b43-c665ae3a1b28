import { NextRequest, NextResponse } from 'next/server';
import { generatePaddleCheckoutUrl } from '@/lib/paddle/utils';

// Endpoint simplificado para suscripciones a Paddle
// En lugar de crear la suscripción en el backend, simplemente generamos la URL de checkout
export async function POST(request: NextRequest) {
  console.log('🔄 Recibida solicitud POST a /api/paddle/subscribe');
  
  try {
    // Extraer datos de la solicitud
    const body = await request.json().catch(e => {
      console.error('❌ Error al parsear el cuerpo de la solicitud:', e);
      return null;
    });
    
    if (!body) {
      console.error('❌ Cuerpo de solicitud vacío o inválido');
      return NextResponse.json(
        { error: 'Cuerpo de solicitud inválido' },
        { status: 400 }
      );
    }
    
    const { user, billingCycle } = body;
    console.log('📦 Datos recibidos:', { user: user?.email, billingCycle });

    if (!user || !user.email) {
      console.error('❌ Falta información del usuario');
      return NextResponse.json(
        { error: 'Se requiere información del usuario' },
        { status: 400 }
      );
    }

    if (!billingCycle || !['monthly', 'yearly'].includes(billingCycle)) {
      console.error('❌ Ciclo de facturación inválido:', billingCycle);
      return NextResponse.json(
        { error: 'Ciclo de facturación debe ser "monthly" o "yearly"' },
        { status: 400 }
      );
    }

    console.log('🔗 Generando URL de checkout de Paddle...');
    
    // Generar URL de checkout con el email del usuario
    const checkoutUrl = generatePaddleCheckoutUrl(billingCycle, user.email);
    
    console.log('✅ URL de checkout generada:', checkoutUrl);

    // Devolver la URL de checkout para que el cliente redirija directamente
    const responseData = {
      checkout_url: checkoutUrl,
      billing_cycle: billingCycle,
      user_email: user.email
    };
    
    console.log('📤 Enviando respuesta:', JSON.stringify(responseData, null, 2));
    return NextResponse.json(responseData);
    
  } catch (error: any) {
    console.error('❌ Error al generar URL de checkout de Paddle:', error);
    return NextResponse.json(
      { error: error.message || 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
