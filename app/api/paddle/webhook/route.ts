import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';
import { getPaddleCustomer, verifyPaddleWebhookSignature } from '@/lib/paddle/utils';
import { 
  PaddleWebhookEvent, 
  PaddleSubscriptionActivatedEvent,
  PaddleSubscriptionCanceledEvent,
  PaddleSubscriptionUpdatedEvent,
  PaddleSubscriptionPastDueEvent,
  PaddleSubscriptionPausedEvent,
  PaddleSubscriptionResumedEvent
} from '@/lib/paddle/types';

// Verificación de Firebase Admin
try {
  console.log('Verificando conexión a Firebase...');
  db.collection('_test').doc('_test').get()
    .then(() => console.log('✅ Firebase Admin verificado correctamente'))
    .catch(err => console.error('❌ Error verificando Firebase Admin:', err));
} catch (error) {
  console.error('❌ Error al verificar Firebase Admin:', error);
}

export async function POST(request: NextRequest) {
  console.log('🔔 Webhook de Paddle recibido');
  
  try {
    // Obtener el cuerpo del request como texto
    const requestBodyText = await request.text();
    console.log('📥 Cuerpo del webhook recibido (primeros 500 chars):', requestBodyText.substring(0, 500));
    
    // Obtener headers para verificación
    const headers: Record<string, string> = {};
    request.headers.forEach((value, key) => {
      headers[key.toLowerCase()] = value;
    });
    
    // Parsear el cuerpo como JSON
    let requestBody: PaddleWebhookEvent;
    try {
      requestBody = JSON.parse(requestBodyText);
    } catch (parseError) {
      console.error('❌ Error al parsear JSON del webhook:', parseError);
      return NextResponse.json({ error: 'JSON inválido' }, { status: 400 });
    }
    
    // Verificar la firma del webhook (si está implementada)
    const signature = headers['paddle-signature'] || '';
    const isValid = await verifyPaddleWebhookSignature(requestBodyText, signature);
    
    if (!isValid) {
      console.error('❌ Firma de webhook inválida');
      return NextResponse.json({ error: 'Firma inválida' }, { status: 401 });
    }
    
    console.log('✅ Webhook verificado:', requestBody.event_type);
    console.log('📌 Event ID:', requestBody.event_id);
    console.log('📅 Occurred at:', requestBody.occurred_at);
    
    // Procesar el evento según su tipo
    switch (requestBody.event_type) {
      case 'subscription.activated':
        await handleSubscriptionActivated(requestBody as PaddleSubscriptionActivatedEvent);
        break;
      case 'subscription.canceled':
        await handleSubscriptionCanceled(requestBody as PaddleSubscriptionCanceledEvent);
        break;
      case 'subscription.updated':
        await handleSubscriptionUpdated(requestBody as PaddleSubscriptionUpdatedEvent);
        break;
      case 'subscription.past_due':
        await handleSubscriptionPastDue(requestBody as PaddleSubscriptionPastDueEvent);
        break;
      case 'subscription.paused':
        await handleSubscriptionPaused(requestBody as PaddleSubscriptionPausedEvent);
        break;
      case 'subscription.resumed':
        await handleSubscriptionResumed(requestBody as PaddleSubscriptionResumedEvent);
        break;
      default:
        console.log(`⚠️ Evento no manejado: ${requestBody.event_type}`);
        // Aún así devolvemos 200 para que Paddle no reintente
        break;
    }
    
    console.log('✅ Webhook procesado exitosamente');
    return NextResponse.json({ success: true });
    
  } catch (error: any) {
    console.error('❌ Error procesando webhook de Paddle:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// Función para obtener el userId basado en el customer_id de Paddle
async function getUserIdFromCustomerId(customerId: string): Promise<string | null> {
  try {
    console.log('🔍 Buscando userId para customer_id:', customerId);
    
    // Obtener información del customer desde Paddle
    const customer = await getPaddleCustomer(customerId);
    console.log('📧 Email del customer:', customer.email);
    
    // Buscar el usuario por email en Firestore
    const usersSnapshot = await db.collection('users')
      .where('email', '==', customer.email)
      .limit(1)
      .get();
    
    if (usersSnapshot.empty) {
      console.error('❌ No se encontró usuario con email:', customer.email);
      return null;
    }
    
    const userId = usersSnapshot.docs[0].id;
    console.log('✅ Usuario encontrado:', userId);
    
    return userId;
  } catch (error) {
    console.error('❌ Error al obtener userId desde customer_id:', error);
    return null;
  }
}

// Función para determinar el estado de acceso basado en el status de Paddle
function determineAccessStatus(paddleStatus: string, currentPeriodEnd: Date | null): {
  subscriptionStatus: 'active' | 'inactive';
  hasAccess: boolean;
  reason: string;
} {
  const now = new Date();

  switch (paddleStatus) {
    case 'active':
      return {
        subscriptionStatus: 'active',
        hasAccess: true,
        reason: 'Suscripción activa'
      };

    case 'paused':
      // Pausada: mantiene acceso hasta el final del período actual
      const hasAccessPaused = currentPeriodEnd ? now < currentPeriodEnd : false;
      return {
        subscriptionStatus: hasAccessPaused ? 'active' : 'inactive',
        hasAccess: hasAccessPaused,
        reason: hasAccessPaused ? 'Pausada - acceso hasta fin de período' : 'Pausada - período expirado'
      };

    case 'past_due':
      // Pago vencido: mantiene acceso hasta el final del período actual
      const hasAccessPastDue = currentPeriodEnd ? now < currentPeriodEnd : false;
      return {
        subscriptionStatus: hasAccessPastDue ? 'active' : 'inactive',
        hasAccess: hasAccessPastDue,
        reason: hasAccessPastDue ? 'Pago vencido - acceso hasta fin de período' : 'Pago vencido - período expirado'
      };

    case 'canceled':
      // Cancelada: sin acceso inmediato (Paddle ya manejó el período de gracia)
      return {
        subscriptionStatus: 'inactive',
        hasAccess: false,
        reason: 'Suscripción cancelada'
      };

    default:
      return {
        subscriptionStatus: 'inactive',
        hasAccess: false,
        reason: `Estado desconocido: ${paddleStatus}`
      };
  }
}

// Función para actualizar datos de usuario y suscripción en Firestore
async function updateUserSubscriptionData(
  userId: string,
  subscriptionData: any,
  customerId: string
): Promise<void> {
  try {
    console.log('📝 Actualizando datos de suscripción para usuario:', userId);
    console.log('📊 Estado de Paddle:', subscriptionData.status);

    const batch = db.batch();

    // Determinar fechas importantes
    const currentPeriodEnd = subscriptionData.current_billing_period?.ends_at ?
      new Date(subscriptionData.current_billing_period.ends_at) : null;
    const nextBillingDate = subscriptionData.next_billed_at ?
      new Date(subscriptionData.next_billed_at) : null;

    // Determinar estado de acceso
    const accessStatus = determineAccessStatus(subscriptionData.status, currentPeriodEnd);
    console.log('🔍 Estado de acceso determinado:', accessStatus);

    // Actualizar documento de usuario
    const userRef = db.collection('users').doc(userId);
    const userUpdateData = {
      subscriptionStatus: accessStatus.subscriptionStatus,
      subscriptionProvider: 'paddle',
      paddleCustomerId: customerId,
      paddleSubscriptionId: subscriptionData.id,
      subscriptionPlanId: subscriptionData.items[0]?.price?.id || null,
      nextBillingDate: nextBillingDate,
      subscriptionEndDate: currentPeriodEnd,
      subscriptionCancelled: subscriptionData.status === 'canceled',
      subscriptionSuspended: subscriptionData.status === 'past_due' || subscriptionData.status === 'paused',
      subscriptionPaused: subscriptionData.status === 'paused',
      subscriptionPastDue: subscriptionData.status === 'past_due',
      paddleStatus: subscriptionData.status, // Guardar estado original de Paddle
      accessReason: accessStatus.reason, // Razón del estado de acceso
      finalAccessDate: currentPeriodEnd, // Fecha hasta cuando tiene acceso
      updatedAt: new Date()
    };
    
    batch.update(userRef, userUpdateData);
    
    // Actualizar documento de suscripción
    const subscriptionRef = db.collection('subscriptions').doc(userId);
    const subscriptionUpdateData = {
      userId: userId,
      subscriptionId: subscriptionData.id,
      status: subscriptionData.status,
      provider: 'paddle',
      customerId: customerId,
      planId: subscriptionData.items[0]?.price?.id || null,
      billingCycle: subscriptionData.billing_cycle,
      currentPeriodStart: subscriptionData.current_billing_period?.starts_at ? 
        new Date(subscriptionData.current_billing_period.starts_at) : null,
      currentPeriodEnd: subscriptionData.current_billing_period?.ends_at ? 
        new Date(subscriptionData.current_billing_period.ends_at) : null,
      nextBillingDate: subscriptionData.next_billed_at ? new Date(subscriptionData.next_billed_at) : null,
      cancelAtPeriodEnd: subscriptionData.status === 'canceled',
      createdAt: new Date(subscriptionData.created_at),
      updatedAt: new Date()
    };
    
    batch.set(subscriptionRef, subscriptionUpdateData, { merge: true });
    
    // Crear/actualizar mapping
    const mappingRef = db.collection('subscription_mappings').doc(subscriptionData.id);
    const mappingData = {
      userId: userId,
      provider: 'paddle',
      providerSubscriptionId: subscriptionData.id,
      paddleCustomerId: customerId,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    batch.set(mappingRef, mappingData, { merge: true });
    
    // Crear entrada en historial
    const historyRef = db.collection('subscription_history').doc();
    const historyData = {
      userId: userId,
      subscriptionId: subscriptionData.id,
      provider: 'paddle',
      event: 'subscription_updated',
      status: subscriptionData.status,
      timestamp: new Date(),
      data: subscriptionData
    };
    
    batch.set(historyRef, historyData);
    
    // Ejecutar todas las operaciones
    await batch.commit();
    console.log('✅ Datos de suscripción actualizados exitosamente');
    
  } catch (error) {
    console.error('❌ Error al actualizar datos de suscripción:', error);
    throw error;
  }
}

// Manejador para suscripción activada
async function handleSubscriptionActivated(event: PaddleSubscriptionActivatedEvent) {
  try {
    console.log('🎉 Procesando suscripción activada:', event.data.id);

    const subscription = event.data;
    const customerId = subscription.customer_id;

    // Obtener userId
    const userId = await getUserIdFromCustomerId(customerId);
    if (!userId) {
      console.error('❌ No se pudo obtener userId para customer_id:', customerId);
      return;
    }

    // Actualizar datos en Firestore
    await updateUserSubscriptionData(userId, subscription, customerId);

    console.log('✅ Suscripción activada procesada exitosamente');
  } catch (error) {
    console.error('❌ Error procesando suscripción activada:', error);
    throw error;
  }
}

// Manejador para suscripción cancelada
async function handleSubscriptionCanceled(event: PaddleSubscriptionCanceledEvent) {
  try {
    console.log('❌ Procesando suscripción cancelada:', event.data.id);

    const subscription = event.data;
    const customerId = subscription.customer_id;

    // Obtener userId
    const userId = await getUserIdFromCustomerId(customerId);
    if (!userId) {
      console.error('❌ No se pudo obtener userId para customer_id:', customerId);
      return;
    }

    // Actualizar datos en Firestore - la función ya maneja la lógica de cancelación
    await updateUserSubscriptionData(userId, subscription, customerId);

    // Para cancelaciones, el acceso se quita inmediatamente
    // No programamos tareas adicionales ya que Paddle maneja el período de gracia
    console.log('🚫 Acceso removido inmediatamente por cancelación');

    console.log('✅ Suscripción cancelada procesada exitosamente');
  } catch (error) {
    console.error('❌ Error procesando suscripción cancelada:', error);
    throw error;
  }
}

// Manejador para suscripción actualizada
async function handleSubscriptionUpdated(event: PaddleSubscriptionUpdatedEvent) {
  try {
    console.log('🔄 Procesando suscripción actualizada:', event.data.id);

    const subscription = event.data;
    const customerId = subscription.customer_id;

    // Obtener userId
    const userId = await getUserIdFromCustomerId(customerId);
    if (!userId) {
      console.error('❌ No se pudo obtener userId para customer_id:', customerId);
      return;
    }

    // Actualizar datos en Firestore
    await updateUserSubscriptionData(userId, subscription, customerId);

    console.log('✅ Suscripción actualizada procesada exitosamente');
  } catch (error) {
    console.error('❌ Error procesando suscripción actualizada:', error);
    throw error;
  }
}

// Manejador para suscripción con pago vencido
async function handleSubscriptionPastDue(event: PaddleSubscriptionPastDueEvent) {
  try {
    console.log('⚠️ Procesando suscripción con pago vencido:', event.data.id);

    const subscription = event.data;
    const customerId = subscription.customer_id;

    // Obtener userId
    const userId = await getUserIdFromCustomerId(customerId);
    if (!userId) {
      console.error('❌ No se pudo obtener userId para customer_id:', customerId);
      return;
    }

    // Actualizar datos en Firestore - mantiene acceso hasta fin del período
    await updateUserSubscriptionData(userId, subscription, customerId);

    const currentPeriodEnd = subscription.current_billing_period?.ends_at;
    if (currentPeriodEnd) {
      console.log('⏰ Usuario mantiene acceso hasta:', new Date(currentPeriodEnd).toISOString());

      // Programar tarea para quitar acceso al final del período si no se resuelve el pago
      const scheduledTaskRef = db.collection('scheduled_tasks').doc();
      await scheduledTaskRef.set({
        userId: userId,
        type: 'check_past_due_access',
        scheduledFor: new Date(currentPeriodEnd),
        data: {
          subscriptionId: subscription.id,
          provider: 'paddle',
          reason: 'past_due_period_ended'
        },
        status: 'pending',
        createdAt: new Date()
      });
    }

    console.log('✅ Suscripción con pago vencido procesada exitosamente');
  } catch (error) {
    console.error('❌ Error procesando suscripción con pago vencido:', error);
    throw error;
  }
}

// Manejador para suscripción pausada
async function handleSubscriptionPaused(event: PaddleSubscriptionPausedEvent) {
  try {
    console.log('⏸️ Procesando suscripción pausada:', event.data.id);

    const subscription = event.data;
    const customerId = subscription.customer_id;

    // Obtener userId
    const userId = await getUserIdFromCustomerId(customerId);
    if (!userId) {
      console.error('❌ No se pudo obtener userId para customer_id:', customerId);
      return;
    }

    // Actualizar datos en Firestore - mantiene acceso hasta fin del período
    await updateUserSubscriptionData(userId, subscription, customerId);

    const currentPeriodEnd = subscription.current_billing_period?.ends_at;
    if (currentPeriodEnd) {
      console.log('⏰ Usuario mantiene acceso hasta:', new Date(currentPeriodEnd).toISOString());

      // Programar tarea para quitar acceso al final del período si sigue pausada
      const scheduledTaskRef = db.collection('scheduled_tasks').doc();
      await scheduledTaskRef.set({
        userId: userId,
        type: 'check_paused_access',
        scheduledFor: new Date(currentPeriodEnd),
        data: {
          subscriptionId: subscription.id,
          provider: 'paddle',
          reason: 'paused_period_ended'
        },
        status: 'pending',
        createdAt: new Date()
      });
    }

    console.log('✅ Suscripción pausada procesada exitosamente');
  } catch (error) {
    console.error('❌ Error procesando suscripción pausada:', error);
    throw error;
  }
}

// Manejador para suscripción reanudada
async function handleSubscriptionResumed(event: PaddleSubscriptionResumedEvent) {
  try {
    console.log('▶️ Procesando suscripción reanudada:', event.data.id);

    const subscription = event.data;
    const customerId = subscription.customer_id;

    // Obtener userId
    const userId = await getUserIdFromCustomerId(customerId);
    if (!userId) {
      console.error('❌ No se pudo obtener userId para customer_id:', customerId);
      return;
    }

    // Actualizar datos en Firestore - restaura acceso completo
    await updateUserSubscriptionData(userId, subscription, customerId);

    // Cancelar cualquier tarea programada de quitar acceso
    try {
      const scheduledTasksSnapshot = await db.collection('scheduled_tasks')
        .where('userId', '==', userId)
        .where('data.subscriptionId', '==', subscription.id)
        .where('status', '==', 'pending')
        .get();

      const batch = db.batch();
      scheduledTasksSnapshot.docs.forEach(doc => {
        batch.update(doc.ref, { status: 'cancelled', cancelledAt: new Date() });
      });

      if (!scheduledTasksSnapshot.empty) {
        await batch.commit();
        console.log('🗑️ Canceladas tareas programadas de quitar acceso');
      }
    } catch (error) {
      console.error('⚠️ Error cancelando tareas programadas:', error);
    }

    console.log('✅ Suscripción reanudada procesada exitosamente - acceso restaurado');
  } catch (error) {
    console.error('❌ Error procesando suscripción reanudada:', error);
    throw error;
  }
}
