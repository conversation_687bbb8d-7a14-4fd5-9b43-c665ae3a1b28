import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';
import { getPaddleCustomer, verifyPaddleWebhookSignature } from '@/lib/paddle/utils';
import { 
  PaddleWebhookEvent, 
  PaddleSubscriptionActivatedEvent,
  PaddleSubscriptionCanceledEvent,
  PaddleSubscriptionUpdatedEvent,
  PaddleSubscriptionPastDueEvent,
  PaddleSubscriptionPausedEvent,
  PaddleSubscriptionResumedEvent
} from '@/lib/paddle/types';

// Verificación de Firebase Admin
try {
  console.log('Verificando conexión a Firebase...');
  db.collection('_test').doc('_test').get()
    .then(() => console.log('✅ Firebase Admin verificado correctamente'))
    .catch(err => console.error('❌ Error verificando Firebase Admin:', err));
} catch (error) {
  console.error('❌ Error al verificar Firebase Admin:', error);
}

export async function POST(request: NextRequest) {
  console.log('🔔 Webhook de Paddle recibido');
  
  try {
    // Obtener el cuerpo del request como texto
    const requestBodyText = await request.text();
    console.log('📥 Cuerpo del webhook recibido (primeros 500 chars):', requestBodyText.substring(0, 500));
    
    // Obtener headers para verificación
    const headers: Record<string, string> = {};
    request.headers.forEach((value, key) => {
      headers[key.toLowerCase()] = value;
    });
    
    // Parsear el cuerpo como JSON
    let requestBody: PaddleWebhookEvent;
    try {
      requestBody = JSON.parse(requestBodyText);
    } catch (parseError) {
      console.error('❌ Error al parsear JSON del webhook:', parseError);
      return NextResponse.json({ error: 'JSON inválido' }, { status: 400 });
    }
    
    // Verificar la firma del webhook (si está implementada)
    const signature = headers['paddle-signature'] || '';
    const isValid = await verifyPaddleWebhookSignature(requestBodyText, signature);
    
    if (!isValid) {
      console.error('❌ Firma de webhook inválida');
      return NextResponse.json({ error: 'Firma inválida' }, { status: 401 });
    }
    
    console.log('✅ Webhook verificado:', requestBody.event_type);
    console.log('📌 Event ID:', requestBody.event_id);
    console.log('📅 Occurred at:', requestBody.occurred_at);
    
    // Procesar el evento según su tipo
    switch (requestBody.event_type) {
      case 'subscription.activated':
        await handleSubscriptionActivated(requestBody as PaddleSubscriptionActivatedEvent);
        break;
      case 'subscription.canceled':
        await handleSubscriptionCanceled(requestBody as PaddleSubscriptionCanceledEvent);
        break;
      case 'subscription.updated':
        await handleSubscriptionUpdated(requestBody as PaddleSubscriptionUpdatedEvent);
        break;
      case 'subscription.past_due':
        await handleSubscriptionPastDue(requestBody as PaddleSubscriptionPastDueEvent);
        break;
      case 'subscription.paused':
        await handleSubscriptionPaused(requestBody as PaddleSubscriptionPausedEvent);
        break;
      case 'subscription.resumed':
        await handleSubscriptionResumed(requestBody as PaddleSubscriptionResumedEvent);
        break;
      default:
        console.log(`⚠️ Evento no manejado: ${requestBody.event_type}`);
        // Aún así devolvemos 200 para que Paddle no reintente
        break;
    }
    
    console.log('✅ Webhook procesado exitosamente');
    return NextResponse.json({ success: true });
    
  } catch (error: any) {
    console.error('❌ Error procesando webhook de Paddle:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// Función para obtener el userId basado en el customer_id de Paddle
async function getUserIdFromCustomerId(customerId: string): Promise<string | null> {
  try {
    console.log('🔍 Buscando userId para customer_id:', customerId);
    
    // Obtener información del customer desde Paddle
    const customer = await getPaddleCustomer(customerId);
    console.log('📧 Email del customer:', customer.email);
    
    // Buscar el usuario por email en Firestore
    const usersSnapshot = await db.collection('users')
      .where('email', '==', customer.email)
      .limit(1)
      .get();
    
    if (usersSnapshot.empty) {
      console.error('❌ No se encontró usuario con email:', customer.email);
      return null;
    }
    
    const userId = usersSnapshot.docs[0].id;
    console.log('✅ Usuario encontrado:', userId);
    
    return userId;
  } catch (error) {
    console.error('❌ Error al obtener userId desde customer_id:', error);
    return null;
  }
}

// Función para actualizar datos de usuario y suscripción en Firestore
async function updateUserSubscriptionData(
  userId: string, 
  subscriptionData: any, 
  customerId: string
): Promise<void> {
  try {
    console.log('📝 Actualizando datos de suscripción para usuario:', userId);
    
    const batch = db.batch();
    
    // Actualizar documento de usuario
    const userRef = db.collection('users').doc(userId);
    const userUpdateData = {
      subscriptionStatus: subscriptionData.status === 'active' ? 'active' : 'inactive',
      subscriptionProvider: 'paddle',
      paddleCustomerId: customerId,
      paddleSubscriptionId: subscriptionData.id,
      subscriptionPlanId: subscriptionData.items[0]?.price?.id || null,
      nextBillingDate: subscriptionData.next_billed_at ? new Date(subscriptionData.next_billed_at) : null,
      subscriptionEndDate: subscriptionData.current_billing_period?.ends_at ? 
        new Date(subscriptionData.current_billing_period.ends_at) : null,
      subscriptionCancelled: subscriptionData.status === 'canceled',
      subscriptionSuspended: subscriptionData.status === 'past_due' || subscriptionData.status === 'paused',
      updatedAt: new Date()
    };
    
    batch.update(userRef, userUpdateData);
    
    // Actualizar documento de suscripción
    const subscriptionRef = db.collection('subscriptions').doc(userId);
    const subscriptionUpdateData = {
      userId: userId,
      subscriptionId: subscriptionData.id,
      status: subscriptionData.status,
      provider: 'paddle',
      customerId: customerId,
      planId: subscriptionData.items[0]?.price?.id || null,
      billingCycle: subscriptionData.billing_cycle,
      currentPeriodStart: subscriptionData.current_billing_period?.starts_at ? 
        new Date(subscriptionData.current_billing_period.starts_at) : null,
      currentPeriodEnd: subscriptionData.current_billing_period?.ends_at ? 
        new Date(subscriptionData.current_billing_period.ends_at) : null,
      nextBillingDate: subscriptionData.next_billed_at ? new Date(subscriptionData.next_billed_at) : null,
      cancelAtPeriodEnd: subscriptionData.status === 'canceled',
      createdAt: new Date(subscriptionData.created_at),
      updatedAt: new Date()
    };
    
    batch.set(subscriptionRef, subscriptionUpdateData, { merge: true });
    
    // Crear/actualizar mapping
    const mappingRef = db.collection('subscription_mappings').doc(subscriptionData.id);
    const mappingData = {
      userId: userId,
      provider: 'paddle',
      providerSubscriptionId: subscriptionData.id,
      paddleCustomerId: customerId,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    batch.set(mappingRef, mappingData, { merge: true });
    
    // Crear entrada en historial
    const historyRef = db.collection('subscription_history').doc();
    const historyData = {
      userId: userId,
      subscriptionId: subscriptionData.id,
      provider: 'paddle',
      event: 'subscription_updated',
      status: subscriptionData.status,
      timestamp: new Date(),
      data: subscriptionData
    };
    
    batch.set(historyRef, historyData);
    
    // Ejecutar todas las operaciones
    await batch.commit();
    console.log('✅ Datos de suscripción actualizados exitosamente');
    
  } catch (error) {
    console.error('❌ Error al actualizar datos de suscripción:', error);
    throw error;
  }
}

// Manejador para suscripción activada
async function handleSubscriptionActivated(event: PaddleSubscriptionActivatedEvent) {
  try {
    console.log('🎉 Procesando suscripción activada:', event.data.id);

    const subscription = event.data;
    const customerId = subscription.customer_id;

    // Obtener userId
    const userId = await getUserIdFromCustomerId(customerId);
    if (!userId) {
      console.error('❌ No se pudo obtener userId para customer_id:', customerId);
      return;
    }

    // Actualizar datos en Firestore
    await updateUserSubscriptionData(userId, subscription, customerId);

    console.log('✅ Suscripción activada procesada exitosamente');
  } catch (error) {
    console.error('❌ Error procesando suscripción activada:', error);
    throw error;
  }
}

// Manejador para suscripción cancelada
async function handleSubscriptionCanceled(event: PaddleSubscriptionCanceledEvent) {
  try {
    console.log('❌ Procesando suscripción cancelada:', event.data.id);

    const subscription = event.data;
    const customerId = subscription.customer_id;

    // Obtener userId
    const userId = await getUserIdFromCustomerId(customerId);
    if (!userId) {
      console.error('❌ No se pudo obtener userId para customer_id:', customerId);
      return;
    }

    // Actualizar datos en Firestore con estado cancelado
    await updateUserSubscriptionData(userId, subscription, customerId);

    // Programar tarea para downgrade si es necesario
    const finalAccessDate = subscription.current_billing_period?.ends_at ?
      new Date(subscription.current_billing_period.ends_at) : new Date();

    const scheduledTaskRef = db.collection('scheduled_tasks').doc();
    await scheduledTaskRef.set({
      userId: userId,
      type: 'downgrade_subscription',
      scheduledFor: finalAccessDate,
      data: {
        subscriptionId: subscription.id,
        provider: 'paddle',
        reason: 'subscription_canceled'
      },
      status: 'pending',
      createdAt: new Date()
    });

    console.log('✅ Suscripción cancelada procesada exitosamente');
  } catch (error) {
    console.error('❌ Error procesando suscripción cancelada:', error);
    throw error;
  }
}

// Manejador para suscripción actualizada
async function handleSubscriptionUpdated(event: PaddleSubscriptionUpdatedEvent) {
  try {
    console.log('🔄 Procesando suscripción actualizada:', event.data.id);

    const subscription = event.data;
    const customerId = subscription.customer_id;

    // Obtener userId
    const userId = await getUserIdFromCustomerId(customerId);
    if (!userId) {
      console.error('❌ No se pudo obtener userId para customer_id:', customerId);
      return;
    }

    // Actualizar datos en Firestore
    await updateUserSubscriptionData(userId, subscription, customerId);

    console.log('✅ Suscripción actualizada procesada exitosamente');
  } catch (error) {
    console.error('❌ Error procesando suscripción actualizada:', error);
    throw error;
  }
}

// Manejador para suscripción con pago vencido
async function handleSubscriptionPastDue(event: PaddleSubscriptionPastDueEvent) {
  try {
    console.log('⚠️ Procesando suscripción con pago vencido:', event.data.id);

    const subscription = event.data;
    const customerId = subscription.customer_id;

    // Obtener userId
    const userId = await getUserIdFromCustomerId(customerId);
    if (!userId) {
      console.error('❌ No se pudo obtener userId para customer_id:', customerId);
      return;
    }

    // Actualizar datos en Firestore marcando como suspendida
    await updateUserSubscriptionData(userId, subscription, customerId);

    console.log('✅ Suscripción con pago vencido procesada exitosamente');
  } catch (error) {
    console.error('❌ Error procesando suscripción con pago vencido:', error);
    throw error;
  }
}

// Manejador para suscripción pausada
async function handleSubscriptionPaused(event: PaddleSubscriptionPausedEvent) {
  try {
    console.log('⏸️ Procesando suscripción pausada:', event.data.id);

    const subscription = event.data;
    const customerId = subscription.customer_id;

    // Obtener userId
    const userId = await getUserIdFromCustomerId(customerId);
    if (!userId) {
      console.error('❌ No se pudo obtener userId para customer_id:', customerId);
      return;
    }

    // Actualizar datos en Firestore
    await updateUserSubscriptionData(userId, subscription, customerId);

    console.log('✅ Suscripción pausada procesada exitosamente');
  } catch (error) {
    console.error('❌ Error procesando suscripción pausada:', error);
    throw error;
  }
}

// Manejador para suscripción reanudada
async function handleSubscriptionResumed(event: PaddleSubscriptionResumedEvent) {
  try {
    console.log('▶️ Procesando suscripción reanudada:', event.data.id);

    const subscription = event.data;
    const customerId = subscription.customer_id;

    // Obtener userId
    const userId = await getUserIdFromCustomerId(customerId);
    if (!userId) {
      console.error('❌ No se pudo obtener userId para customer_id:', customerId);
      return;
    }

    // Actualizar datos en Firestore
    await updateUserSubscriptionData(userId, subscription, customerId);

    console.log('✅ Suscripción reanudada procesada exitosamente');
  } catch (error) {
    console.error('❌ Error procesando suscripción reanudada:', error);
    throw error;
  }
}
