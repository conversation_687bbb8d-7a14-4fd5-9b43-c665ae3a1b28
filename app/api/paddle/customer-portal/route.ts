import { NextRequest, NextResponse } from 'next/server';
import { createCustomerPortalSession } from '@/lib/paddle/utils';
import { db } from '@/lib/firebase-admin';

// Endpoint para crear una sesión de customer portal de Paddle
export async function POST(request: NextRequest) {
  console.log('🔄 Recibida solicitud POST a /api/paddle/customer-portal');
  
  try {
    // Extraer datos de la solicitud
    const body = await request.json().catch(e => {
      console.error('❌ Error al parsear el cuerpo de la solicitud:', e);
      return null;
    });
    
    if (!body) {
      console.error('❌ Cuerpo de solicitud vacío o inválido');
      return NextResponse.json(
        { error: 'Cuerpo de solicitud inválido' },
        { status: 400 }
      );
    }
    
    const { userId } = body;
    console.log('📦 Datos recibidos:', { userId });

    if (!userId) {
      console.error('❌ Falta userId');
      return NextResponse.json(
        { error: 'Se requiere userId' },
        { status: 400 }
      );
    }

    console.log('🔍 Buscando información de suscripción de Paddle para usuario:', userId);

    // Buscar el customer_id y subscription_id de Paddle en Firestore
    let customerId: string | null = null;
    let subscriptionIds: string[] = [];

    try {
      // 1. Verificar en documento de usuario
      const userDoc = await db.collection('users').doc(userId).get();
      if (userDoc.exists) {
        const userData = userDoc.data();
        
        if (userData?.paddleCustomerId) {
          customerId = userData.paddleCustomerId;
          console.log('✅ Customer ID encontrado en usuario:', customerId);
        }
        
        if (userData?.paddleSubscriptionId) {
          subscriptionIds.push(userData.paddleSubscriptionId);
          console.log('✅ Subscription ID encontrado en usuario:', userData.paddleSubscriptionId);
        }
      }

      // 2. Verificar en colección de suscripciones
      if (!customerId || subscriptionIds.length === 0) {
        const subscriptionDoc = await db.collection('subscriptions').doc(userId).get();
        if (subscriptionDoc.exists) {
          const subscriptionData = subscriptionDoc.data();
          
          if (!customerId && subscriptionData?.paddleCustomerId) {
            customerId = subscriptionData.paddleCustomerId;
            console.log('✅ Customer ID encontrado en suscripción:', customerId);
          }
          
          if (subscriptionIds.length === 0 && subscriptionData?.paddleSubscriptionId) {
            subscriptionIds.push(subscriptionData.paddleSubscriptionId);
            console.log('✅ Subscription ID encontrado en suscripción:', subscriptionData.paddleSubscriptionId);
          }
        }
      }

      // 3. Buscar en mappings si aún no tenemos la información
      if (!customerId || subscriptionIds.length === 0) {
        const mappingsSnapshot = await db.collection('subscription_mappings')
          .where('userId', '==', userId)
          .where('provider', '==', 'paddle')
          .limit(1)
          .get();

        if (!mappingsSnapshot.empty) {
          const mappingData = mappingsSnapshot.docs[0].data();
          
          if (!customerId && mappingData.paddleCustomerId) {
            customerId = mappingData.paddleCustomerId;
            console.log('✅ Customer ID encontrado en mapping:', customerId);
          }
          
          if (subscriptionIds.length === 0 && mappingData.providerSubscriptionId) {
            subscriptionIds.push(mappingData.providerSubscriptionId);
            console.log('✅ Subscription ID encontrado en mapping:', mappingData.providerSubscriptionId);
          }
        }
      }

    } catch (firestoreError) {
      console.error('❌ Error al buscar en Firestore:', firestoreError);
    }

    // Verificar que tenemos la información necesaria
    if (!customerId) {
      console.error('❌ No se encontró customer_id de Paddle para el usuario');
      return NextResponse.json(
        { error: 'No se encontró información de customer de Paddle para este usuario' },
        { status: 404 }
      );
    }

    if (subscriptionIds.length === 0) {
      console.error('❌ No se encontraron subscription_ids de Paddle para el usuario');
      return NextResponse.json(
        { error: 'No se encontraron suscripciones de Paddle para este usuario' },
        { status: 404 }
      );
    }

    console.log('🔗 Creando sesión de customer portal...');
    console.log('👤 Customer ID:', customerId);
    console.log('📋 Subscription IDs:', subscriptionIds);

    // Crear la sesión de customer portal
    const portalUrl = await createCustomerPortalSession(customerId, subscriptionIds);
    
    console.log('✅ Sesión de customer portal creada');

    // Devolver la URL del portal
    const responseData = {
      portal_url: portalUrl,
      customer_id: customerId,
      subscription_ids: subscriptionIds
    };
    
    console.log('📤 Enviando respuesta con URL del portal');
    return NextResponse.json(responseData);
    
  } catch (error: any) {
    console.error('❌ Error al crear sesión de customer portal:', error);
    return NextResponse.json(
      { error: error.message || 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
