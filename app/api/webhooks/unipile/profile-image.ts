import { v2 as cloudinary } from 'cloudinary';
import { db, database as rtdb } from '@/lib/firebase-admin';

// Configurar Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'tatu-ink',
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
  secure: true
});

// Función para registrar logs detallados
function logDebug(message: string, data?: any) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] 🔍 DEBUG: ${message}`);
  if (data) {
    console.log(JSON.stringify(data, null, 2));
  }
}

// Función para registrar errores
function logError(message: string, error?: any) {
  const timestamp = new Date().toISOString();
  console.error(`[${timestamp}] ❌ ERROR: ${message}`);
  if (error) {
    console.error(error);
  }
}

const UNIPILE_API_BASE = process.env.UNIPILE_API_URL || process.env.NEXT_PUBLIC_UNIPILE_API_URL || 'https://api10.unipile.com:14039';
const UNIPILE_API_URL = `${UNIPILE_API_BASE}/api/v1`;
const UNIPILE_API_KEY = process.env.UNIPILE_API_KEY || process.env.NEXT_PUBLIC_UNIPILE_API_KEY || '0cpnYoIG.uOdj54YoC5lIxFRiuFXaec2txbzrFV5Wh1HF74f68xY=';

/**
 * Obtiene y guarda la imagen de perfil de un participante o grupo de Unipile
 * @param id ID del participante o chat en Unipile
 * @param providerId ID único del proveedor del participante o grupo (más estable que id)
 * @param userId ID del usuario (tatuador)
 * @param conversationId ID de la conversación
 * @param clientId ID del cliente (opcional)
 * @param isGroup Indica si se trata de un grupo (true) o un participante individual (false)
 * @returns URL de la imagen en Cloudinary o null si falló
 */
export async function fetchAndSaveProfilePicture(
  id: string,
  providerId: string,
  userId: string,
  conversationId: string,
  clientId?: string,
  isGroup: boolean = false
): Promise<string | null> {
  // Crear un ID único para este proceso para seguimiento en logs
  const processId = `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

  // Validar parámetros para evitar errores
  if (!id || !userId || !conversationId) {
    logError(`[${processId}] Parámetros incompletos para obtener imagen de perfil`, { id, userId, conversationId });
    return null;
  }

  logDebug(`[${processId}] Iniciando proceso de obtención de imagen de perfil`, {
    id,
    providerId,
    userId,
    conversationId,
    clientId: clientId || 'no proporcionado',
    isGroup
  });
  try {
    console.log(`🖼️ Obteniendo imagen de perfil para ${isGroup ? 'grupo' : 'participante'}: ${id}`);

    // 1. Obtener la imagen de perfil de Unipile
    // Usar el endpoint correcto según si es un grupo o un participante individual
    const endpointPath = isGroup ? `chats/${id}/picture` : `chat_attendees/${id}/picture`;

    logDebug(`[${processId}] Haciendo solicitud a API de Unipile`, {
      url: `${UNIPILE_API_URL}/${endpointPath}`,
      method: 'GET',
      headers: {
        'X-API-KEY': '***REDACTED***',
        'Accept': '*/*'
      },
      isGroup
    });

    try {
      // Crear una URL para la solicitud
      const apiUrl = `${UNIPILE_API_URL}/${endpointPath}`;

      // Implementar un mecanismo de reintentos con backoff exponencial
      let response = null;
      let attempt = 0;
      const maxAttempts = 3;

      while (attempt < maxAttempts) {
        try {
          attempt++;
          logDebug(`[${processId}] Intento ${attempt}/${maxAttempts} de obtener imagen de Unipile`);

          // Hacer la solicitud a la API de Unipile con un timeout
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 segundos de timeout

          response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
              'X-API-KEY': UNIPILE_API_KEY,
              'Accept': '*/*' // Aceptar cualquier tipo de contenido, no solo JSON
            },
            signal: controller.signal
          });

          clearTimeout(timeoutId);

          // Si la respuesta es exitosa, salir del bucle
          if (response.ok) {
            logDebug(`[${processId}] Imagen obtenida con éxito en el intento ${attempt}`);
            break;
          }

          // Si la respuesta no es exitosa, esperar antes de reintentar
          const waitTime = Math.min(Math.pow(2, attempt) * 1000, 8000); // Backoff exponencial, máximo 8 segundos
          logDebug(`[${processId}] Respuesta no exitosa (${response.status}), esperando ${waitTime}ms antes de reintentar`);
          await new Promise(resolve => setTimeout(resolve, waitTime));

        } catch (fetchError: any) {
          // Si es el último intento, propagar el error
          if (attempt >= maxAttempts) {
            throw fetchError;
          }

          // Si no es el último intento, esperar antes de reintentar
          const waitTime = Math.min(Math.pow(2, attempt) * 1000, 8000); // Backoff exponencial, máximo 8 segundos
          logDebug(`[${processId}] Error al obtener imagen: ${fetchError.message}, esperando ${waitTime}ms antes de reintentar`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }

      // Verificar que tenemos una respuesta válida
      if (!response) {
        logError(`[${processId}] No se recibió respuesta de la API de Unipile después de ${maxAttempts} intentos`);
        return null;
      }

      logDebug(`[${processId}] Respuesta recibida de API de Unipile`, {
        status: response.status,
        statusText: response.statusText,
        contentType: response.headers.get('content-type'),
        contentLength: response.headers.get('content-length')
      });

      // Verificar si la respuesta es exitosa
      if (!response.ok) {
        logError(`[${processId}] Error al obtener imagen de perfil`, {
          status: response.status,
          statusText: response.statusText
        });

        // Intentar obtener más información del error
        try {
          const errorText = await response.text();
          logError(`[${processId}] Contenido del error:`, errorText);
        } catch (textError) {
          logError(`[${processId}] No se pudo obtener el contenido del error`, textError);
        }

        return null;
      }

      // Verificar el tipo de contenido
      const contentType = response.headers.get('content-type');

      // Si el contenido no es una imagen, puede ser un error en formato JSON
      if (contentType && contentType.includes('application/json')) {
        try {
          const errorData = await response.json();
          logError(`[${processId}] Error devuelto por la API en formato JSON`, errorData);
          return null;
        } catch (jsonError) {
          logError(`[${processId}] Error al parsear respuesta JSON`, jsonError);
        }
      }

      // 2. Convertir la respuesta a un buffer
      const arrayBuffer = await response.arrayBuffer();
      logDebug(`[${processId}] Datos binarios recibidos`, {
        byteLength: arrayBuffer.byteLength,
        contentType
      });

      // Verificar si recibimos datos
      if (arrayBuffer.byteLength === 0) {
        logError(`[${processId}] La API devolvió un array vacío`);
        return null;
      }

      // Crear un buffer a partir del array
      const buffer = Buffer.from(arrayBuffer);

      // Detectar el tipo de archivo basado en los magic bytes
      // Esta es una forma más precisa que depender solo del content-type
      let mimeType = contentType || 'image/jpeg';
      let fileExtension = '.jpg';

      // Detectar tipo basado en magic bytes (bytes iniciales del archivo)
      // Usar subarray en lugar de slice para evitar la advertencia de obsolescencia
      const magicBytes = buffer.subarray(0, 16);
      const magicHex = magicBytes.toString('hex').toLowerCase();

      logDebug(`[${processId}] Magic bytes detectados`, { magicHex });

      // JPEG: comienza con FFD8
      if (magicHex.startsWith('ffd8')) {
        mimeType = 'image/jpeg';
        fileExtension = '.jpg';
      }
      // PNG: comienza con 89504E47 (‰PNG)
      else if (magicHex.startsWith('89504e47')) {
        mimeType = 'image/png';
        fileExtension = '.png';
      }
      // GIF: comienza con 474946 (GIF)
      else if (magicHex.startsWith('474946')) {
        mimeType = 'image/gif';
        fileExtension = '.gif';
      }
      // WEBP: contiene WEBP en los primeros bytes
      else if (magicHex.includes('57454250')) {
        mimeType = 'image/webp';
        fileExtension = '.webp';
      }

      logDebug(`[${processId}] Tipo de archivo detectado`, { mimeType, fileExtension });

      // 3. Crear un base64 para subir a Cloudinary
      const base64Data = `data:${mimeType};base64,${buffer.toString('base64')}`;
      logDebug(`[${processId}] Base64 creado para subir a Cloudinary`, {
        length: base64Data.length,
        mimeType
      });

      // 4. Subir a Cloudinary
      logDebug(`[${processId}] Iniciando subida a Cloudinary...`);

      // Generar un nombre único para la imagen usando providerId que es estable
      // Limpiar el providerId para usarlo como parte del nombre del archivo
      const cleanProviderId = providerId.replace(/[^a-zA-Z0-9]/g, '_');
      const uniqueId = `${userId}_${cleanProviderId}`;
      const folder = isGroup ? 'group-pictures' : 'profile-pictures';

      // Configurar opciones de subida a Cloudinary siguiendo el mismo patrón que para adjuntos
      const uploadOptions = {
        folder,
        public_id: uniqueId,
        resource_type: 'auto' as 'auto', // Detectar automáticamente el tipo de recurso
        format: fileExtension.replace('.', ''), // Especificar el formato sin el punto
        transformation: [
          { quality: 'auto' }, // Optimizar calidad
          { width: 400, height: 400, crop: 'limit' }, // Limitar tamaño máximo
          { dpr: '2.0' } // Alta resolución para pantallas retina
        ]
      };

      logDebug(`[${processId}] Configuración de subida a Cloudinary`, uploadOptions);

      // Subir la imagen a Cloudinary siguiendo el mismo patrón que para adjuntos
      try {
        // Crear una promesa para la subida a Cloudinary
        const uploadResult: any = await new Promise((resolve, reject) => {
          logDebug(`[${processId}] Iniciando subida a Cloudinary con opciones`, {
            folder: uploadOptions.folder,
            public_id: uploadOptions.public_id,
            format: uploadOptions.format,
            resource_type: uploadOptions.resource_type
          });

          cloudinary.uploader.upload(
            base64Data,
            uploadOptions,
            (error, result) => {
              if (error) {
                logError(`[${processId}] Error de Cloudinary durante la subida`, error);
                reject(error);
              } else {
                logDebug(`[${processId}] Subida a Cloudinary exitosa`, {
                  public_id: result?.public_id,
                  secure_url: result?.secure_url,
                  format: result?.format,
                  width: result?.width,
                  height: result?.height,
                  bytes: result?.bytes
                });
                resolve(result);
              }
            }
          );
        });

        // Verificar que tenemos una URL segura
        if (!uploadResult || !uploadResult.secure_url) {
          logError(`[${processId}] Error al subir imagen a Cloudinary: No se recibió URL`, uploadResult);
          return null;
        }

        // Obtener la URL de la imagen
        const imageUrl = uploadResult.secure_url;

        // ===== LOG DETALLADO: IMAGEN OBTENIDA DE CLOUDINARY =====
        console.log(`\n\n========== VERIFICACIÓN DE IMAGEN DE PERFIL ==========`);
        console.log(`✅ IMAGEN OBTENIDA DE CLOUDINARY EXITOSAMENTE`);
        console.log(`📝 URL de la imagen: "${imageUrl}"`);
        console.log(`📊 Detalles de la imagen:`);
        console.log(`   - ID público: ${uploadResult.public_id}`);
        console.log(`   - Formato: ${uploadResult.format}`);
        console.log(`   - Tipo: ${uploadResult.resource_type}`);
        console.log(`   - Tamaño: ${uploadResult.width}x${uploadResult.height}`);
        console.log(`   - Bytes: ${uploadResult.bytes}`);
        console.log(`   - Carpeta: ${uploadResult.folder}`);
        console.log(`========== FIN DE VERIFICACIÓN DE CLOUDINARY ==========\n\n`);

        logDebug(`[${processId}] Imagen de perfil subida exitosamente a Cloudinary`, {
          url: imageUrl,
          public_id: uploadResult.public_id,
          folder: uploadResult.folder,
          format: uploadResult.format,
          type: uploadResult.resource_type,
          size: `${uploadResult.width}x${uploadResult.height}`,
          bytes: uploadResult.bytes
        });

        // Ahora que tenemos la URL de la imagen, actualizamos las bases de datos

        // 5. Actualizar la conversación en Firestore
        try {
          const conversationRef = db.collection('conversations').doc(conversationId);
          const conversationDoc = await conversationRef.get();

          if (conversationDoc.exists) {
            await conversationRef.update({
              profilePictureUrl: imageUrl,
              participantPicture: imageUrl, // También actualizamos este campo para compatibilidad
              updatedAt: new Date()
            });
            logDebug(`[${processId}] Conversación actualizada en Firestore`, {
              conversationId,
              imageUrl
            });
          } else {
            logDebug(`[${processId}] La conversación no existe en Firestore`, { conversationId });
          }
        } catch (firestoreError) {
          logError(`[${processId}] Error al actualizar conversación en Firestore`, firestoreError);
          // Continuamos aunque falle esta parte
        }

        // 6. Actualizar la conversación en Realtime Database
        try {
          // Usar la instancia de RTDB importada correctamente
          // No necesitamos crear una nueva instancia, ya la importamos al inicio del archivo
          // const rtdb = db.database(); // Esta línea era incorrecta

          // Primero, intentamos actualizar directamente la conversación con el ID proporcionado
          const rtdbConversationRef = rtdb.ref(`conversations/${userId}/${conversationId}`);
          const conversationSnapshot = await rtdbConversationRef.get();

          if (conversationSnapshot.exists()) {
            // Obtener los datos actuales de la conversación
            const conversationData = conversationSnapshot.val();

            // Preparar los datos para la actualización
            const updateData: any = {
              profilePictureUrl: imageUrl,
              participantPicture: imageUrl // También actualizamos este campo para compatibilidad
            };

            // Si ya existe el campo participant, lo actualizamos preservando los datos existentes
            if (conversationData.participant) {
              updateData.participant = {
                ...conversationData.participant,
                profilePic: imageUrl, // Añadir la imagen en la estructura que espera ConversationList
                profilePictureUrl: imageUrl // Añadir también este campo para compatibilidad
              };
            } else {
              // Si no existe, lo creamos con la información mínima necesaria
              // Necesitamos asegurarnos de incluir id y name que son campos obligatorios
              updateData.participant = {
                id: conversationData.participantId || id || `user-${Date.now()}`,
                name: conversationData.participantName || 'Usuario',
                profilePic: imageUrl,
                profilePictureUrl: imageUrl
              };
            }

            // Registrar la estructura de datos que estamos guardando
            logDebug(`[${processId}] Estructura de datos para actualizar en RTDB`, updateData);

            // Actualizar solo si la conversación existe
            await rtdbConversationRef.update(updateData);

            // Verificar si la actualización se realizó correctamente
            try {
              // Esperar un momento para asegurarse de que la actualización se haya propagado
              await new Promise(resolve => setTimeout(resolve, 500));

              // Leer la conversación actualizada
              const updatedSnapshot = await rtdbConversationRef.get();
              const updatedData = updatedSnapshot.val();

              // ===== LOG DETALLADO: VERIFICACIÓN DE RTDB =====
              console.log(`\n\n========== VERIFICACIÓN DE RTDB ==========`);
              if (updatedData && (updatedData.profilePictureUrl === imageUrl ||
                                 updatedData.participantPicture === imageUrl ||
                                 (updatedData.participant && updatedData.participant.profilePic === imageUrl))) {
                console.log(`✅ VERIFICACIÓN EXITOSA: URL GUARDADA CORRECTAMENTE EN RTDB`);
                console.log(`📝 URL guardada en RTDB: "${updatedData.profilePictureUrl || updatedData.participantPicture || (updatedData.participant && updatedData.participant.profilePic)}"`);
                console.log(`📊 Ruta en RTDB: conversations/${userId}/${conversationId}`);
                console.log(`📊 Datos actualizados en RTDB:`);
                console.log(`   - profilePictureUrl: ${updatedData.profilePictureUrl || 'no existe'}`);
                console.log(`   - participantPicture: ${updatedData.participantPicture || 'no existe'}`);
                console.log(`   - participant.profilePic: ${updatedData.participant?.profilePic || 'no existe'}`);
              } else {
                console.log(`❌ VERIFICACIÓN FALLIDA: URL NO GUARDADA EN RTDB`);
                console.log(`📝 URL esperada: "${imageUrl}"`);
                console.log(`📊 Ruta en RTDB: conversations/${userId}/${conversationId}`);
                console.log(`📊 Datos actuales en RTDB:`);
                console.log(JSON.stringify(updatedData, null, 2));
              }
              console.log(`========== FIN DE VERIFICACIÓN DE RTDB ==========\n\n`);
            } catch (verifyError: any) {
              console.log(`\n\n========== ERROR EN VERIFICACIÓN DE RTDB ==========`);
              console.log(`❌ ERROR AL VERIFICAR DATOS EN RTDB: ${verifyError.message}`);
              console.log(`📝 URL que se intentó guardar: "${imageUrl}"`);
              console.log(`📊 Ruta en RTDB: conversations/${userId}/${conversationId}`);
              console.log(`========== FIN DE ERROR EN VERIFICACIÓN ==========\n\n`);
            }

            logDebug(`[${processId}] Conversación actualizada en RTDB`, {
              path: `conversations/${userId}/${conversationId}`,
              imageUrl
            });

            // Guardar un registro de la actualización exitosa en RTDB en lugar de Firestore
            try {
              // Crear una clave única para esta actualización
              const updateKey = providerId || id || `update-${Date.now()}`;
              const profileUpdateRef = rtdb.ref(`profileUpdates/${userId}/${updateKey}`);

              const updateData = {
                userId,
                conversationId,
                id,
                providerId,
                isGroup,
                imageUrl,
                timestamp: Date.now(),
                success: true,
                processId
              };

              // DEBUGGING INTENSIVO: Verificar que rtdb es válido
              logDebug(`[${processId}] Verificando que la instancia de RTDB es válida: ${!!rtdb}`);
              logDebug(`[${processId}] Tipo de rtdb: ${typeof rtdb}`);
              logDebug(`[${processId}] Métodos disponibles en rtdb: ${Object.keys(rtdb).join(', ')}`);

              // DEBUGGING: Verificar que la actualización es posible
              logDebug(`[${processId}] URL de referencia completa: ${profileUpdateRef.toString()}`);

              // Registrar la estructura de datos que estamos guardando
              logDebug(`[${processId}] Estructura de datos para registro de actualización en RTDB`, updateData);

              try {
                // Intentar actualización y capturar cualquier error
                await profileUpdateRef.set(updateData);

                // Verificar si el registro se guardó correctamente
                try {
                  // Esperar un momento para asegurarse de que la actualización se haya propagado
                  await new Promise(resolve => setTimeout(resolve, 500));

                  // Leer el registro actualizado
                  const updatedLogSnapshot = await profileUpdateRef.get();
                  const updatedLogData = updatedLogSnapshot.val();

                  // ===== LOG DETALLADO: VERIFICACIÓN DE REGISTRO EN RTDB =====
                  console.log(`\n\n========== VERIFICACIÓN DE REGISTRO EN RTDB ==========`);
                  if (updatedLogData && updatedLogData.imageUrl === imageUrl) {
                    console.log(`✅ VERIFICACIÓN EXITOSA: REGISTRO GUARDADO CORRECTAMENTE EN RTDB`);
                    console.log(`📝 URL guardada en registro: "${updatedLogData.imageUrl}"`);
                    console.log(`📊 Ruta del registro: profileUpdates/${userId}/${updateKey}`);
                    console.log(`📊 Datos del registro:`);
                    console.log(JSON.stringify(updatedLogData, null, 2));
                  } else {
                    console.log(`❌ VERIFICACIÓN FALLIDA: REGISTRO NO GUARDADO CORRECTAMENTE EN RTDB`);
                    console.log(`📝 URL esperada: "${imageUrl}"`);
                    console.log(`📊 Ruta del registro: profileUpdates/${userId}/${updateKey}`);
                    console.log(`📊 Datos actuales:`);
                    console.log(JSON.stringify(updatedLogData, null, 2));
                  }
                  console.log(`========== FIN DE VERIFICACIÓN DE REGISTRO ==========\n\n`);
                } catch (verifyLogError: any) {
                  console.log(`\n\n========== ERROR EN VERIFICACIÓN DE REGISTRO ==========`);
                  console.log(`❌ ERROR AL VERIFICAR REGISTRO EN RTDB: ${verifyLogError.message}`);
                  console.log(`📝 URL que se intentó guardar: "${imageUrl}"`);
                  console.log(`📊 Ruta del registro: profileUpdates/${userId}/${updateKey}`);
                  console.log(`========== FIN DE ERROR EN VERIFICACIÓN DE REGISTRO ==========\n\n`);
                }

                logDebug(`[${processId}] ✅ Registro de actualización guardado con éxito en RTDB en la ruta: profileUpdates/${userId}/${updateKey}`);
              } catch (error: any) {
                // Capturar y registrar cualquier error en detalle
                logError(`[${processId}] ❌ ERROR CRÍTICO al guardar en RTDB: ${error.message}`);
                logError(`[${processId}] Stack de error:`, error.stack);
                logError(`[${processId}] Código de error:`, error.code);

                // Intentar con una ruta diferente como último recurso
                try {
                  const fallbackRef = rtdb.ref(`profileUpdatesFallback/${Date.now()}`);
                  await fallbackRef.set({
                    ...updateData,
                    error: error.message,
                    errorTimestamp: Date.now(),
                    fallback: true
                  });
                  logDebug(`[${processId}] ⚠️ Guardado en ruta alternativa como fallback`);
                } catch (fallbackError: any) {
                  logError(`[${processId}] ❌ Error total, también falló el fallback: ${fallbackError.message}`);
                }
              }
            } catch (error) {
              logError(`[${processId}] Error al guardar registro de actualización en RTDB`, error);
            }

            return imageUrl; // Retornar la URL de la imagen si la actualización fue exitosa
          } else {
            logDebug(`[${processId}] La conversación no existe en RTDB, buscando alternativas`, {
              conversationId
            });

            // Buscar todas las conversaciones del usuario
            const allConversationsRef = rtdb.ref(`conversations/${userId}`);
            const allConversationsSnapshot = await allConversationsRef.get();

            if (allConversationsSnapshot.exists()) {
              const conversationsCount = Object.keys(allConversationsSnapshot.val() || {}).length;
              logDebug(`[${processId}] Buscando entre ${conversationsCount} conversaciones`);

              let found = false;
              let updatedConversationId = null;

              // Convertir a array para facilitar el procesamiento
              const conversationsArray = Object.entries(allConversationsSnapshot.val() || {})
                .map(([key, value]: [string, any]) => ({
                  id: key,
                  ...value
                }));

              // 1. Primero buscar por coincidencia exacta de IDs
              for (const conv of conversationsArray) {
                if (
                  conv.chatId === conversationId ||
                  conv.metaSenderId === conversationId ||
                  conv.id === conversationId ||
                  conv.externalId === conversationId ||
                  conv.superchatChannelId === conversationId
                ) {
                  const matchingConversationRef = rtdb.ref(`conversations/${userId}/${conv.id}`);

                  // Obtener los datos actuales de la conversación
                  const matchingConvSnapshot = await matchingConversationRef.get();
                  const matchingConvData = matchingConvSnapshot.exists() ? matchingConvSnapshot.val() : {};

                  // Preparar los datos para la actualización
                  const updateData: any = {
                    profilePictureUrl: imageUrl,
                    participantPicture: imageUrl,
                    attendeeId: id, // Guardar el ID para futuras referencias
                  };

                  // Si ya existe el campo participant, lo actualizamos preservando los datos existentes
                  if (matchingConvData.participant) {
                    updateData.participant = {
                      ...matchingConvData.participant,
                      profilePic: imageUrl, // Añadir la imagen en la estructura que espera ConversationList
                      profilePictureUrl: imageUrl // Añadir también este campo para compatibilidad
                    };
                  } else {
                    // Si no existe, lo creamos con la información mínima necesaria
                    // Necesitamos asegurarnos de incluir id y name que son campos obligatorios
                    updateData.participant = {
                      id: matchingConvData.participantId || id || `user-${Date.now()}`,
                      name: matchingConvData.participantName || 'Usuario',
                      profilePic: imageUrl,
                      profilePictureUrl: imageUrl
                    };
                  }

                  // Registrar la estructura de datos que estamos guardando
                  logDebug(`[${processId}] Estructura de datos para actualizar conversación alternativa en RTDB`, updateData);

                  // Actualizar la conversación
                  await matchingConversationRef.update(updateData);

                  logDebug(`[${processId}] Conversación alternativa actualizada`, {
                    conversationId: conv.id,
                    matchType: 'ID exacto',
                    imageUrl
                  });

                  found = true;
                  updatedConversationId = conv.id;
                  break;
                }
              }

              // 2. Si no encontramos por ID, buscar por attendeeId
              if (!found) {
                for (const conv of conversationsArray) {
                  if (conv.attendeeId === id) {
                    try {
                      // @ts-ignore - Ignoramos el error de TypeScript ya que sabemos que db.database() existe
                      const rtdb = db.database();
                      const rtdbConversationRef = rtdb.ref(`conversations/${userId}/${conv.id}`);

                      // Primero obtenemos la conversación actual para asegurarnos de no sobrescribir datos
                      const conversationSnapshot = await rtdbConversationRef.once('value');
                      const conversationData = conversationSnapshot.exists() ? conversationSnapshot.val() : {};

                      // Preparar los datos para la actualización
                      const updateData: any = {
                        profilePictureUrl: imageUrl,
                        participantPicture: imageUrl
                      };

                      // Si ya existe el campo participant, lo actualizamos preservando los datos existentes
                      if (conversationData.participant) {
                        updateData.participant = {
                          ...conversationData.participant,
                          profilePic: imageUrl
                        };
                      } else {
                        // Si no existe, lo creamos con la información mínima necesaria
                        updateData.participant = {
                          profilePic: imageUrl
                        };
                      }

                      // Actualizar la conversación con la URL de la imagen
                      await rtdbConversationRef.update(updateData);
                    } catch (error) {
                      logError(`[${processId}] Error al actualizar conversación en RTDB`, error);
                    }

                    logDebug(`[${processId}] Conversación encontrada por attendeeId`, {
                      conversationId: conv.id,
                      id,
                      imageUrl
                    });

                    found = true;
                    updatedConversationId = conv.id;
                    break;
                  }
                }
              }

              // 3. Si aún no encontramos, actualizar la conversación más reciente sin imagen
              if (!found) {
                logDebug(`[${processId}] No se encontró coincidencia exacta, buscando conversación reciente sin imagen`);

                // Filtrar conversaciones sin imagen y ordenar por más recientes
                const conversationsWithoutImage = conversationsArray
                  .filter(conv => !conv.profilePictureUrl && !conv.participantPicture)
                  .sort((a, b) => (b.lastMessageAt || 0) - (a.lastMessageAt || 0));

                if (conversationsWithoutImage.length > 0) {
                  const mostRecentConv = conversationsWithoutImage[0];
                  const recentConvRef = rtdb.ref(`conversations/${userId}/${mostRecentConv.id}`);

                  // Obtener los datos actuales de la conversación
                  const recentConvSnapshot = await recentConvRef.get();
                  const recentConvData = recentConvSnapshot.exists() ? recentConvSnapshot.val() : {};

                  // Preparar los datos para la actualización
                  const updateData: any = {
                    profilePictureUrl: imageUrl,
                    participantPicture: imageUrl,
                    attendeeId: id
                  };

                  // Si ya existe el campo participant, lo actualizamos preservando los datos existentes
                  if (recentConvData.participant) {
                    updateData.participant = {
                      ...recentConvData.participant,
                      profilePic: imageUrl,
                      profilePictureUrl: imageUrl
                    };
                  } else {
                    // Si no existe, lo creamos con la información mínima necesaria
                    updateData.participant = {
                      id: recentConvData.participantId || id || `user-${Date.now()}`,
                      name: recentConvData.participantName || 'Usuario',
                      profilePic: imageUrl,
                      profilePictureUrl: imageUrl
                    };
                  }

                  // Registrar la estructura de datos que estamos guardando
                  logDebug(`[${processId}] Estructura de datos para actualizar conversación reciente en RTDB`, updateData);

                  await recentConvRef.update(updateData);

                  logDebug(`[${processId}] Conversación más reciente actualizada`, {
                    conversationId: mostRecentConv.id,
                    matchType: 'más reciente sin imagen',
                    imageUrl
                  });

                  found = true;
                  updatedConversationId = mostRecentConv.id;
                }
              }

              if (found) {
                // Guardar un registro de la actualización exitosa
                try {
                  await db.collection('profilePictureUpdates').add({
                    userId,
                    originalConversationId: conversationId,
                    updatedConversationId,
                    attendeeId: id,
                    imageUrl,
                    timestamp: new Date(),
                    success: true
                  });
                } catch (error) {
                  logError(`[${processId}] Error al guardar registro de actualización`, error);
                }

                return imageUrl; // Retornar la URL de la imagen si la actualización fue exitosa
              } else {
                logDebug(`[${processId}] No se encontró ninguna conversación para actualizar`);
              }
            } else {
              logDebug(`[${processId}] No hay conversaciones para este usuario`, { userId });
            }
          }
        } catch (rtdbError) {
          logError(`[${processId}] Error al actualizar conversación en RTDB`, rtdbError);
        }

        // 7. Si hay un clientId, actualizar también el perfil del cliente
        if (clientId) {
          try {
            const clientRef = db.collection('clients').doc(clientId);
            const clientDoc = await clientRef.get();

            if (clientDoc.exists) {
              await clientRef.update({
                profilePictureUrl: imageUrl,
                participantPicture: imageUrl, // También actualizamos este campo para compatibilidad
                updatedAt: new Date()
              });
              logDebug(`[${processId}] Cliente actualizado con imagen de perfil`, {
                clientId,
                imageUrl
              });
            } else {
              logDebug(`[${processId}] El cliente no existe`, { clientId });
            }
          } catch (clientError) {
            logError(`[${processId}] Error al actualizar cliente`, clientError);
            // Continuamos aunque falle esta parte
          }
        }

        // Guardar un registro de la actualización SOLO en RTDB, evitando Firestore por completo
        try {
          // Crear una clave única para este registro
          const updateKey = `${id || ''}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
          const rtdbUpdateRef = rtdb.ref(`profilePictureUpdates/${userId}/${updateKey}`);

          const updateData = {
            userId,
            conversationId,
            id,
            clientId: clientId || null,
            imageUrl,
            timestamp: Date.now(),
            success: true,
            processId
          };

          // Intentar guardar en RTDB con manejo de errores detallado
          try {
            await rtdbUpdateRef.set(updateData);

            // Verificar si el registro final se guardó correctamente
            try {
              // Esperar un momento para asegurarse de que la actualización se haya propagado
              await new Promise(resolve => setTimeout(resolve, 500));

              // Leer el registro actualizado
              const finalLogSnapshot = await rtdbUpdateRef.get();
              const finalLogData = finalLogSnapshot.val();

              // ===== LOG DETALLADO: VERIFICACIÓN DE REGISTRO FINAL EN RTDB =====
              console.log(`\n\n========== VERIFICACIÓN DE REGISTRO FINAL EN RTDB ==========`);
              if (finalLogData && finalLogData.imageUrl === imageUrl) {
                console.log(`✅ VERIFICACIÓN EXITOSA: REGISTRO FINAL GUARDADO CORRECTAMENTE EN RTDB`);
                console.log(`📝 URL guardada en registro final: "${finalLogData.imageUrl}"`);
                console.log(`📊 Ruta del registro final: profilePictureUpdates/${userId}/${updateKey}`);
                console.log(`📊 Datos del registro final:`);
                console.log(JSON.stringify(finalLogData, null, 2));
              } else {
                console.log(`❌ VERIFICACIÓN FALLIDA: REGISTRO FINAL NO GUARDADO CORRECTAMENTE EN RTDB`);
                console.log(`📝 URL esperada: "${imageUrl}"`);
                console.log(`📊 Ruta del registro final: profilePictureUpdates/${userId}/${updateKey}`);
                console.log(`📊 Datos actuales:`);
                console.log(JSON.stringify(finalLogData, null, 2));
              }
              console.log(`========== FIN DE VERIFICACIÓN DE REGISTRO FINAL ==========\n\n`);
            } catch (verifyFinalLogError: any) {
              console.log(`\n\n========== ERROR EN VERIFICACIÓN DE REGISTRO FINAL ==========`);
              console.log(`❌ ERROR AL VERIFICAR REGISTRO FINAL EN RTDB: ${verifyFinalLogError.message}`);
              console.log(`📝 URL que se intentó guardar: "${imageUrl}"`);
              console.log(`📊 Ruta del registro final: profilePictureUpdates/${userId}/${updateKey}`);
              console.log(`========== FIN DE ERROR EN VERIFICACIÓN DE REGISTRO FINAL ==========\n\n`);
            }

            logDebug(`[${processId}] ✅ Registro final guardado con éxito en RTDB`);
          } catch (rtdbError: any) {
            logError(`[${processId}] ❌ Error al guardar registro final en RTDB: ${rtdbError.message}`);

            // Intentar con una ruta alternativa como último recurso
            try {
              const fallbackRef = rtdb.ref(`profileUpdatesFallback/${Date.now()}_${Math.random().toString(36).substring(2, 9)}`);
              await fallbackRef.set({
                ...updateData,
                error: rtdbError.message,
                errorTimestamp: Date.now(),
                fallback: true
              });
              logDebug(`[${processId}] ⚠️ Registro final guardado en ruta alternativa como fallback`);
            } catch (fallbackError: any) {
              logError(`[${processId}] ❌ Error total al guardar registro final, también falló el fallback: ${fallbackError.message}`);
            }
          }
        } catch (error: any) {
          logError(`[${processId}] Error general al guardar registro final: ${error.message}`);
        }

        return imageUrl;
      } catch (uploadError) {
        logError(`[${processId}] Error durante el proceso de subida a Cloudinary`, uploadError);
        return null;
      }
    } catch (fetchError) {
      logError(`[${processId}] Error al hacer fetch de la imagen`, fetchError);
      return null;
    }
  } catch (error) {
    logError(`[${processId}] Error general al procesar y guardar imagen de perfil`, error);
    return null;
  }
}
