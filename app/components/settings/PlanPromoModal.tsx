'use client';

import { Fragment, useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon, CheckIcon, SparklesIcon, TicketIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import { useAuth } from '@/app/contexts/AuthContext';
import { useSettings } from './SettingsContext';

interface PlanPromoModalProps {
  isOpen: boolean;
  onClose: () => void;
}

// Información sobre los planes y sus beneficios según el cupón
type PlanBenefit = {
  name: string;
  freeMonths: number;
  description: string;
};

type PlanBenefitsMap = {
  [key: string]: PlanBenefit;
};

const PLAN_BENEFITS: PlanBenefitsMap = {
  // Plan IDs de Reveniu
  '14880': { name: 'Plan Pro Lanzamiento', freeMonths: 3, description: 'Lanzamiento especial con 3 meses gratis' },
  '14879': { name: 'Plan Pro Ganador Sorteo', freeMonths: 6, description: '¡Felicidades! Has ganado 6 meses gratis' },
  '14878': { name: 'Plan Pro Especial', freeMonths: 11, description: 'Oferta exclusiva con 11 meses gratis' },
  '14877': { name: 'Plan Pro TMF', freeMonths: 3, description: 'Plan especial para jueces con 3 meses gratis' },
  // Planes regulares
  '14875': { name: 'Plan Pro Mensual', freeMonths: 0, description: 'Todas las herramientas que necesitas' },
  '14876': { name: 'Plan Pro Anual', freeMonths: 0, description: 'Ahorra 20% con el plan anual' }
};

export default function PlanPromoModal({ isOpen, onClose }: PlanPromoModalProps) {
  const { user, subscriptionStatus, refreshSubscription } = useAuth();
  const { subscription } = useSettings();
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSubscribing, setIsSubscribing] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  // Solo USD con Paddle - no necesitamos selector de moneda

  // Verificar el estado de la suscripción al abrir el modal
  useEffect(() => {
    if (isOpen && user) {
      console.log('🔍 PlanPromoModal - Verificando estado de suscripción antes de mostrar');

      // Solo intentar actualizar si hay conexión a internet
      if (!navigator.onLine) {
        console.log('⚠️ PlanPromoModal - No hay conexión a internet, usando estado actual');
        // Si el usuario ya tiene una suscripción activa, cerrar el modal
        if (subscriptionStatus === 'active') {
          console.log('✅ PlanPromoModal - Usuario tiene suscripción activa, cerrando modal');
          onClose();
        }
        return;
      }

      // Usar un timeout para evitar esperas prolongadas
      const timeoutId = setTimeout(() => {
        console.log('⚠️ PlanPromoModal - Timeout en verificación de suscripción');
      }, 5000);

      refreshSubscription()
        .then(() => {
          clearTimeout(timeoutId);
          console.log('📊 PlanPromoModal - Estado actualizado:', subscriptionStatus);
          // Si el usuario ya tiene una suscripción activa, cerrar el modal
          if (subscriptionStatus === 'active') {
            console.log('✅ PlanPromoModal - Usuario tiene suscripción activa, cerrando modal');
            onClose();
          }
        })
        .catch((error) => {
          clearTimeout(timeoutId);
          console.error('❌ PlanPromoModal - Error al verificar suscripción:', error);
        });
    }
  }, [isOpen, user, subscriptionStatus, refreshSubscription, onClose]);

  // Añadamos un useEffect para asegurarnos de que el modal de pago se cierre cuando el modal promocional se cierre
  useEffect(() => {
    if (!isOpen) {
      setShowPaymentModal(false);
    }
  }, [isOpen]);

  const freeFeatures = [
    { name: 'Estudio', enabled: true },
    { name: 'Calendario', enabled: true },
    { name: 'Mensajes', enabled: false },
    { name: 'Finanzas', enabled: false },
    { name: 'Clientes', enabled: false },
    { name: 'Portfolio', enabled: true },
    { name: 'Reseñas', enabled: true },
    { name: 'Automatizaciones', enabled: false },
    { name: 'Análisis', enabled: false },
    { name: 'Personalización Sitio', enabled: true, description: 'Básica (logo y nombre)' },
    { name: 'Enlaces', enabled: true, description: 'Agregar enlaces básicos' },
    { name: 'Asistente de IA', enabled: false },
    { name: 'Soporte', enabled: false }
  ];

  const proFeatures = [
    { name: 'Estudio', enabled: true },
    { name: 'Calendario', enabled: true },
    { name: 'Mensajes', enabled: true },
    { name: 'Finanzas', enabled: true },
    { name: 'Clientes', enabled: true },
    { name: 'Portfolio', enabled: true },
    { name: 'Reseñas', enabled: true },
    { name: 'Automatizaciones', enabled: true },
    { name: 'Análisis', enabled: true },
    { name: 'Personalización Sitio', enabled: true, description: 'Completa (branding, estilos personalizados)' },
    { name: 'Enlaces', enabled: true, description: 'Enlaces avanzados (configuración personalizada)' },
    { name: 'Asistente de IA', enabled: true, description: 'Incluido (respuesta automatizada y manejo de consultas)' },
    { name: 'Soporte', enabled: true }
  ];

  // Función para obtener información del plan según el plan ID
  const getPlanInfo = (planId: number | string) => {
    const id = planId.toString();
    return PLAN_BENEFITS[id] || {
      name: 'Plan Pro',
      freeMonths: 0,
      description: 'Todas las herramientas que necesitas'
    };
  };

  // Función simplificada para Paddle - no necesitamos validación de cupones
  // Paddle maneja los descuentos directamente

  // Función simplificada para abrir modal (ya no necesaria con Paddle directo)

  // Función para cerrar el modal de pago
  const closePaymentModal = () => {
    setShowPaymentModal(false);
  };

  // Función para suscribirse usando Paddle directamente
  const handleSubscribe = async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('👤 Usuario actual en modal:', {
        email: user?.email,
        uid: user?.uid,
        displayName: user?.displayName
      });

      if (!user?.email) {
        console.error('❌ No hay email del usuario en modal:', user);
        throw new Error('No se pudo obtener el email del usuario');
      }

      console.log(`📊 Iniciando suscripción al plan: pro`);
      console.log(`🔄 Preparando proceso de pago con Paddle`);

      // Generar URL de Paddle según el ciclo de facturación
      const paddleUrl = billingCycle === 'monthly'
        ? `https://pay.paddle.io/hsc_01jypp2mn7q86qmr1a1qmgbnrq_2jedg02th2hwy2r3apg661spag5jkjnh?theme=dark&user_email=${encodeURIComponent(user.email)}`
        : `https://pay.paddle.io/hsc_01jz1jhbs3dn1p4nd8rrqw0z6g_9gc65ak5b07ygbk7azwrb1egrjxzzb6p?theme=dark&user_email=${encodeURIComponent(user.email)}`;

      console.log('� Abriendo Paddle:', paddleUrl);
      console.log('📧 Email enviado a Paddle desde modal:', user.email);

      // Abrir Paddle en nueva ventana
      window.open(paddleUrl, '_blank');

      // Cerrar el modal actual
      onClose();

    } catch (err) {
      console.error('Error:', err);
      setError(err instanceof Error ? err.message : 'Error al abrir Paddle');
      setIsLoading(false);
    }
  };

  // Paddle maneja todo directamente - no necesitamos funciones adicionales



  return (
    <Transition.Root show={isOpen} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-50"
        onClose={(open) => {
          // Solo cerrar si el modal de pago no está abierto
          if (!showPaymentModal) {
            onClose();
          }
        }}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-4xl">
                {/* Header con gradiente */}
                <div className="bg-gradient-to-r from-gray-900 to-black text-white p-4 relative">
                  <div className="absolute right-2 top-2">
                    <button
                      type="button"
                      className="rounded-full bg-black/20 p-1.5 text-white hover:bg-black/30 focus:outline-none transition-all"
                      onClick={onClose}
                    >
                      <span className="sr-only">Cerrar</span>
                      <XMarkIcon className="h-4 w-4" aria-hidden="true" />
                    </button>
                  </div>

                  <div className="flex items-center mb-1">
                    <SparklesIcon className="h-5 w-5 text-yellow-400 mr-2" />
                    <span className="text-xs font-medium uppercase tracking-wider text-yellow-400">Plan Artista del Tatuaje</span>
                  </div>

                  <Dialog.Title as="h2" className="text-xl font-bold leading-tight">
                    Potencia tu Administración con Tatu Pro
                  </Dialog.Title>

                  <p className="mt-1 text-sm text-gray-300 max-w-2xl">
                    Accede a todas las herramientas profesionales con
                    <span className="font-bold text-white"> 1 mes GRATIS</span> de prueba.
                  </p>
                </div>

                <div className="p-4 sm:p-5">
                  {/* Sección simplificada - Paddle maneja descuentos directamente */}

                  {/* Selector de ciclo de facturación */}
                  <div className="mb-4">
                    <div className="flex justify-center">
                      <div className="bg-gray-100 p-1 rounded-full inline-flex">
                        <button
                          type="button"
                          onClick={() => setBillingCycle('monthly')}
                          className={`rounded-full px-4 py-1.5 text-xs font-medium transition-all ${
                            billingCycle === 'monthly'
                              ? 'bg-white text-gray-900 shadow-sm'
                              : 'text-gray-700 hover:text-gray-900'
                          }`}
                        >
                          Mensual
                        </button>
                        <button
                          type="button"
                          onClick={() => setBillingCycle('yearly')}
                          className={`rounded-full px-4 py-1.5 text-xs font-medium transition-all ${
                            billingCycle === 'yearly'
                              ? 'bg-white text-gray-900 shadow-sm'
                              : 'text-gray-700 hover:text-gray-900'
                          }`}
                        >
                          Anual
                          <span className="ml-1 inline-flex items-center rounded-full bg-green-100 px-1 py-0.5 text-xs font-medium text-green-800">
                            -20%
                          </span>
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Tarjetas de planes */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                    {/* Plan Gratis */}
                    <div className="rounded-xl border border-gray-200 overflow-hidden bg-white">
                      <div className="p-3">
                        <h3 className="text-base font-semibold text-gray-900">Gratis</h3>
                        <p className="text-xs text-gray-500 mb-2">Lo básico para comenzar</p>

                        <div className="mb-3">
                          <p className="text-xl font-bold text-gray-900">$0</p>
                          <p className="text-xs text-gray-500">para siempre</p>
                        </div>

                        <div className="space-y-1.5">
                          {freeFeatures.map((feature, index) => (
                            <div key={index} className="flex items-start">
                              {feature.enabled ? (
                                <CheckIcon className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                              ) : (
                                <XMarkIcon className="h-4 w-4 text-gray-300 mt-0.5 mr-2 flex-shrink-0" />
                              )}
                              <div>
                                <p className={`text-xs ${feature.enabled ? 'text-gray-700' : 'text-gray-400'}`}>
                                  {feature.name}
                                  {feature.description && (
                                    <span className={`block text-xs ${feature.enabled ? 'text-gray-500' : 'text-gray-400'}`}>
                                      {feature.description}
                                    </span>
                                  )}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Plan Pro */}
                    <div className="rounded-xl border-2 border-gray-900 overflow-hidden bg-white relative">
                      {/* Badge de recomendado */}
                      <div className="absolute top-0 right-0">
                        <div className="bg-gray-900 text-white text-xs font-medium px-2 py-0.5 rounded-bl-lg">
                          Recomendado
                        </div>
                      </div>

                      <div className="p-3">
                        <h3 className="text-base font-semibold text-gray-900">Artista del Tatuaje</h3>
                        <p className="text-xs text-gray-500 mb-2">Todas las herramientas que necesitas</p>

                        <div className="mb-2">
                          <p className="text-xl font-bold text-gray-900">
                            {billingCycle === 'monthly' ? '$50' : '$480'}
                            <span className="text-sm font-normal text-gray-500 ml-1">
                              {billingCycle === 'monthly' ? '/mes' : '/año'}
                            </span>
                          </p>

                          {billingCycle === 'monthly' ? (
                            <p className="text-xs font-medium text-green-600 mt-0.5 flex items-center">
                              <SparklesIcon className="h-3 w-3 mr-1" />
                              Pago mensual
                            </p>
                          ) : (
                            <p className="text-xs font-medium text-green-600 mt-0.5 flex items-center">
                              <SparklesIcon className="h-3 w-3 mr-1" />
                              Ahorras $120 (20% descuento)
                            </p>
                          )}
                        </div>

                        <div className="space-y-1.5 mb-3">
                          {proFeatures.map((feature, index) => (
                            <div key={index} className="flex items-start">
                              <CheckIcon className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                              <div>
                                <p className="text-xs text-gray-700">
                                  {feature.name}
                                  {feature.description && (
                                    <span className="block text-xs text-gray-500">
                                      {feature.description}
                                    </span>
                                  )}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>

                        <button
                          type="button"
                          className="w-full flex justify-center items-center rounded-lg bg-gray-900 px-3 py-2 text-xs font-medium text-white shadow-sm hover:bg-gray-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-900 transition-all"
                          onClick={handleSubscribe}
                          disabled={isLoading}
                        >
                          {isLoading ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-2 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Procesando...
                            </>
                          ) : (
                            'Comenzar con Paddle'
                          )}
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="text-center text-xs text-gray-500">
                    Al suscribirte aceptas nuestros <a href="/terms" className="text-gray-900 hover:underline font-medium">Términos de Servicio</a> y <a href="/privacy" className="text-gray-900 hover:underline font-medium">Política de Privacidad</a>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>

      {/* Modal de pago - implementación completa y aislada para dispositivos móviles */}
      {showPaymentModal && (
        // Portal separado para el modal de pago, con el nivel z-index máximo posible
        <div
          className="fixed inset-0 z-[9999]"
          // Forzar a que esto sea un nuevo contexto de apilamiento
          style={{
            isolation: 'isolate',
            // Prevenir cualquier desplazamiento o gestos táctiles
            touchAction: 'none',
            // Asegurar que todos los eventos queden aquí
            userSelect: 'none',
            WebkitUserSelect: 'none'
          }}
          onClick={(e) => {
            // Detener la propagación a nivel global
            e.stopPropagation();
          }}
        >
          {/* Overlay oscuro con eventos habilitados */}
          <div
            className="fixed inset-0 bg-black bg-opacity-80"
            style={{ pointerEvents: 'auto' }}
            onClick={(e) => {
              // Consumir el evento pero no hacer nada
              e.stopPropagation();
            }}
          />

          {/* Contenedor central con pointer-events desactivados */}
          <div
            className="fixed inset-0 overflow-hidden flex items-center justify-center"
            style={{ pointerEvents: 'none' }}
          >
            {/* Contenedor del modal con todos los eventos reactivados */}
            <div
              className="relative mx-4 max-w-3xl w-full bg-white rounded-2xl shadow-2xl overflow-hidden"
              style={{
                pointerEvents: 'auto',
                // Forzar a que este elemento capture todos los eventos
                touchAction: 'auto',
                // Crear un nuevo contexto para elementos internos
                isolation: 'isolate'
              }}
              onClick={(e) => {
                // Prevenir que los clics pasen al fondo
                e.stopPropagation();
              }}
            >
              {/* Cabecera del modal con gradiente */}
              <div className="bg-gradient-to-r from-gray-900 to-black text-white p-3 flex items-center justify-between">
                <div className="flex items-center">
                  <SparklesIcon className="h-4 w-4 text-yellow-400 mr-2" />
                  <span className="font-bold text-base">Finalizar Compra</span>
                </div>

                {/* Botón de cierre con área táctil ampliada */}
                <button
                  type="button"
                  className="rounded-full bg-black/20 p-1.5 text-white hover:bg-black/30 focus:outline-none transition-all"
                  onClick={() => setShowPaymentModal(false)}
                  style={{ touchAction: 'manipulation' }}
                >
                  <XMarkIcon className="h-4 w-4" aria-hidden="true" />
                </button>
              </div>

              {/* Contenido del modal */}
              <div className="grid grid-cols-1 md:grid-cols-2">
                {/* Panel izquierdo - Detalles del plan */}
                <div className="p-3 bg-gray-50">
                  <h3 className="text-base font-bold mb-2 text-gray-900">Resumen de tu Plan</h3>

                  <div className="bg-white rounded-xl border border-gray-200 p-3 mb-3">
                    <div className="flex items-center mb-2">
                      <div className="bg-gray-900 rounded-full p-1.5 mr-2">
                        <SparklesIcon className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm text-gray-900">Plan Artista del Tatuaje</h4>
                        <p className="text-xs text-gray-500">
                          {billingCycle === 'monthly' ? 'Facturación mensual' : 'Facturación anual'}
                        </p>
                      </div>
                    </div>

                    <div className="mb-4">
                      {billingCycle === 'monthly' ? (
                        <div className="flex items-baseline">
                          <span className="text-2xl font-bold text-gray-900">$50</span>
                          <span className="text-gray-600 ml-1 text-sm">/mes</span>
                        </div>
                      ) : (
                        <div>
                          <div className="flex items-baseline">
                            <span className="text-2xl font-bold text-gray-900">$480</span>
                            <span className="text-gray-600 ml-1 text-sm">/año</span>
                          </div>
                          <div className="mt-1">
                            <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-1 text-xs font-medium text-green-800">
                              <CheckIcon className="mr-1 h-3 w-3 text-green-600" />
                              20% descuento
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="space-y-3 text-sm text-gray-600">
                    <div className="flex items-start">
                      <CheckIcon className="h-5 w-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                      <p>Puedes cancelar en cualquier momento</p>
                    </div>

                    <div className="flex items-start">
                      <CheckIcon className="h-5 w-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                      <p className="font-medium text-green-700">
                        {billingCycle === 'monthly'
                          ? "Plan Artista del Tatuaje Mensual"
                          : "Plan Artista del Tatuaje Anual - 20% de descuento"}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Panel derecho - Pago con Paddle */}
                <div className="p-3">
                  <h3 className="text-base font-bold mb-3 text-gray-900">Pago con Paddle</h3>

                  {/* Información del usuario */}
                  <div className="mb-3">
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Información del usuario
                    </label>
                    <div className="p-2 bg-gray-50 border border-gray-200 rounded-lg">
                      <p className="text-xs font-medium text-gray-900">{user?.displayName || 'Usuario'}</p>
                      <p className="text-xs text-gray-600">{user?.email || '<EMAIL>'}</p>
                    </div>
                  </div>

                  {/* Información de Paddle */}
                  <div className="mb-3">
                    <div className="p-2 bg-blue-50 border border-blue-200 rounded-lg">
                      <p className="text-xs font-medium text-blue-800">💳 Pago seguro con Paddle</p>
                      <p className="text-xs text-blue-600">Acepta tarjetas de crédito, débito y más métodos de pago</p>
                    </div>
                  </div>

                  {/* Botón de pago con Paddle */}
                  <div className="space-y-2">
                    <button
                      className="w-full flex justify-center items-center rounded-lg bg-[#6366f1] px-3 py-2 text-xs font-medium text-white shadow-sm hover:bg-[#5856eb] focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[#6366f1] transition-all"
                      onClick={handleSubscribe}
                      disabled={isLoading}
                      style={{ touchAction: 'manipulation' }}
                    >
                      {isLoading ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Abriendo Paddle...
                        </>
                      ) : (
                        <>
                          <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                          </svg>
                          Continuar con Paddle
                        </>
                      )}
                    </button>

                    <button
                      className="w-full flex justify-center items-center rounded-lg bg-white border border-gray-300 px-3 py-2 text-xs font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-300 transition-all"
                      onClick={() => setShowPaymentModal(false)}
                      style={{ touchAction: 'manipulation' }}
                    >
                      Cancelar
                    </button>
                  </div>

                  {/* Mensaje de error si existe */}
                  {error && (
                    <div className="mt-4 rounded-lg bg-red-50 p-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-red-800">Error</h3>
                          <div className="mt-2 text-sm text-red-700">
                            <p>{error}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </Transition.Root>
  );
}