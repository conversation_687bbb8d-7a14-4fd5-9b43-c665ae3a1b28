'use client';

import { motion } from 'framer-motion';
import { Check, X, Zap, Star, Shield, Bot, Calendar, Users, Palette, MessageSquare, BarChart, Clock, Receipt, Send, LineChart, Building, MinusCircle, PlusCircle } from 'lucide-react';
import Link from 'next/link';
import { useState, useEffect } from 'react';

export default function Pricing() {
  const [isAnnual, setIsAnnual] = useState(false);
  const [accountCount, setAccountCount] = useState(2);
  const [isFormOpen, setIsFormOpen] = useState(false);

  // Manejar el cierre del popup cuando se presiona Escape
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsFormOpen(false);
      }
    };

    if (isFormOpen) {
      document.addEventListener('keydown', handleEscKey);
      // Bloquear el scroll del body cuando el popup está abierto
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      // Restaurar el scroll cuando el componente se desmonta o el popup se cierra
      document.body.style.overflow = '';
    };
  }, [isFormOpen]);

  // Precios base del plan Artista del Tatuaje
  const baseMonthlyPrice = 50;
  const baseAnnualPrice = 480;

  // Calcular el descuento por cuenta para el plan Estudio
  const getDiscountPercentage = (accounts: number): number => {
    // 2% de descuento para 2-4 cuentas, 5% para 5-9 cuentas, 10% para 10+ cuentas
    if (accounts >= 2 && accounts <= 4) {
      return 2;
    } else if (accounts >= 5 && accounts <= 9) {
      return 5;
    } else if (accounts >= 10) {
      return 10;
    }
    return 0;
  };

  // Calcular el precio total para el plan Estudio (precio por cuenta con descuento × número de cuentas)
  const calculateStudioPrice = (basePrice: number, accounts: number): number => {
    const discountPercentage = getDiscountPercentage(accounts);
    const pricePerAccount = basePrice * (1 - discountPercentage / 100);
    return Math.round(pricePerAccount * accounts); // Precio por cuenta con descuento × número de cuentas
  };

  // Calcular precio por cuenta con descuento
  const pricePerAccount = isAnnual
    ? (baseAnnualPrice * (1 - getDiscountPercentage(accountCount) / 100)) / 12
    : baseMonthlyPrice * (1 - getDiscountPercentage(accountCount) / 100);

  // Formatear precio para mostrar
  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('es-CL').format(Math.round(price));
  };

  const plans = [
    {
      name: 'Tatuador Independiente',
      price: '0',
      annualPrice: '0',
      description: 'Ideal para tatuadores independientes que están comenzando',
      color: 'from-gray-700 to-gray-900',
      features: [
        {
          name: 'Estudio Digital',
          description: 'Perfil profesional para mostrar tu trabajo',
          included: true
        },
        {
          name: 'Calendario Básico',
          description: 'Gestión simple de citas y disponibilidad',
          included: true
        },
        {
          name: 'Portfolio',
          description: 'Galería para mostrar tus diseños y trabajos',
          included: true
        },
        {
          name: 'Reseñas de Clientes',
          description: 'Recolección y exhibición de testimonios',
          included: true
        },
        {
          name: 'Enlaces Personalizados',
          description: 'URL personalizada para tu perfil',
          included: true
        },
        {
          name: 'Mensajería con Clientes',
          description: 'Chat integrado con tus clientes',
          included: false
        },
        {
          name: 'Gestión de Clientes',
          description: 'Base de datos detallada de clientes',
          included: false
        },
        {
          name: 'Asistente de IA',
          description: 'Filtrado inteligente de clientes potenciales',
          included: false
        },
        {
          name: 'Automatizaciones de Mensajes',
          description: 'Envío automático de mensajes a clientes',
          included: false
        },
        {
          name: 'Gestión de Facturación',
          description: 'Registro de ingresos, egresos e impuestos',
          included: false
        },
        {
          name: 'Análisis del Sitio Web',
          description: 'Estadísticas de visitas, conversiones y comportamiento',
          included: false
        },
        {
          name: 'Soporte Prioritario',
          description: 'Atención personalizada y rápida',
          included: false
        }
      ],
      cta: 'Comenzar Gratis',
      url: '//app.tatu.ink/register',
      popular: false,
      highlightFeatures: [
        {
          icon: Palette,
          name: 'Portfolio Digital'
        },
        {
          icon: Calendar,
          name: 'Calendario Básico'
        }
      ]
    },
    {
      name: 'Artista del Tatuaje',
      price: '50',
      annualPrice: '480',
      description: 'Todo lo que necesitas para hacer crecer tu estudio de tatuajes',
      color: 'from-gray-700 to-gray-900',
      features: [
        {
          name: 'Estudio Digital',
          description: 'Perfil profesional para mostrar tu trabajo',
          included: true
        },
        {
          name: 'Calendario Avanzado',
          description: 'Gestión completa con recordatorios automáticos',
          included: true
        },
        {
          name: 'Portfolio Premium',
          description: 'Galería avanzada con categorías y filtros',
          included: true
        },
        {
          name: 'Reseñas de Clientes',
          description: 'Recolección y exhibición de testimonios',
          included: true
        },
        {
          name: 'Enlaces Personalizados',
          description: 'URL personalizada para tu perfil',
          included: true
        },
        {
          name: 'Mensajería con Clientes',
          description: 'Chat integrado con tus clientes',
          included: true
        },
        {
          name: 'Gestión de Clientes',
          description: 'Base de datos detallada de clientes',
          included: true
        },
        {
          name: 'Asistente de IA',
          description: 'Filtrado inteligente de clientes potenciales',
          included: true
        },
        {
          name: 'Automatizaciones de Mensajes',
          description: 'Envío automático de mensajes a clientes',
          included: true
        },
        {
          name: 'Gestión de Facturación',
          description: 'Registro de ingresos, egresos e impuestos',
          included: true
        },
        {
          name: 'Análisis del Sitio Web',
          description: 'Estadísticas de visitas, conversiones y comportamiento',
          included: true
        },
        {
          name: 'Soporte Prioritario',
          description: 'Atención personalizada y rápida',
          included: true
        }
      ],
      cta: 'Comenzar Prueba Gratis',
      url: '//app.tatu.ink/register',
      popular: true,
      highlightFeatures: [
        {
          icon: Bot,
          name: 'Asistente de IA'
        },
        {
          icon: Send,
          name: 'Automatizaciones'
        },
        {
          icon: LineChart,
          name: 'Analytics'
        }
      ]
    },
    {
      name: 'Estudio',
      price: formatPrice(calculateStudioPrice(baseMonthlyPrice, accountCount)),
      annualPrice: formatPrice(calculateStudioPrice(baseAnnualPrice, accountCount) / 12),
      description: 'Solución completa para estudios con múltiples artistas',
      color: 'from-gray-700 to-gray-900',
      features: [
        {
          name: 'Estudio Digital',
          description: 'Perfil profesional para mostrar tu trabajo',
          included: true
        },
        {
          name: 'Calendario Avanzado',
          description: 'Gestión completa con recordatorios automáticos',
          included: true
        },
        {
          name: 'Portfolio Premium',
          description: 'Galería avanzada con categorías y filtros',
          included: true
        },
        {
          name: 'Reseñas de Clientes',
          description: 'Recolección y exhibición de testimonios',
          included: true
        },
        {
          name: 'Enlaces Personalizados',
          description: 'URL personalizada para tu perfil',
          included: true
        },
        {
          name: 'Mensajería con Clientes',
          description: 'Chat integrado con tus clientes',
          included: true
        },
        {
          name: 'Gestión de Clientes',
          description: 'Base de datos detallada de clientes',
          included: true
        },
        {
          name: 'Asistente de IA',
          description: 'Filtrado inteligente de clientes potenciales',
          included: true
        },
        {
          name: 'Automatizaciones de Mensajes',
          description: 'Envío automático de mensajes a clientes',
          included: true
        },
        {
          name: 'Gestión de Facturación',
          description: 'Registro de ingresos, egresos e impuestos',
          included: true
        },
        {
          name: 'Análisis del Sitio Web',
          description: 'Estadísticas de visitas, conversiones y comportamiento',
          included: true
        },
        {
          name: 'Soporte Prioritario',
          description: 'Atención personalizada y rápida',
          included: true
        },
        {
          name: 'Múltiples Cuentas',
          description: 'Gestión de múltiples artistas bajo un mismo estudio',
          included: true
        },
        {
          name: 'Panel de Administración',
          description: 'Control centralizado de todas las cuentas',
          included: true
        }
      ],
      cta: 'Contactar para Cotizar',
      url: '//app.tatu.ink/register?plan=studio',
      popular: false,
      isStudio: true,
      highlightFeatures: [
        {
          icon: Building,
          name: 'Múltiples Artistas'
        },
        {
          icon: Users,
          name: 'Gestión Centralizada'
        },
        {
          icon: Receipt,
          name: 'Facturación Unificada'
        }
      ]
    }
  ];

  return (
    <section id="pricing" className="py-24 relative overflow-hidden bg-black">
      {/* Popup personalizado para el formulario */}
      {isFormOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-70" onClick={() => setIsFormOpen(false)}>
          <div
            className="relative bg-white rounded-xl w-full max-w-4xl h-[80vh] overflow-hidden shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              className="absolute top-4 right-4 z-10 bg-gray-800 text-white rounded-full p-2 hover:bg-gray-700 transition-colors"
              onClick={() => setIsFormOpen(false)}
            >
              <X className="w-5 h-5" />
            </button>
            <iframe
              src="https://31de5h42.forms.app/plan-para-estudios-de-tatuajes"
              className="w-full h-full border-0"
              title="Formulario para Estudios de Tatuajes"
            />
          </div>
        </div>
      )}

      {/* Elementos decorativos de fondo */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-full h-full bg-[url('/tattoo-pattern.png')] bg-repeat opacity-5"></div>
        <div className="absolute top-40 -right-20 w-80 h-80 bg-gray-700 rounded-full filter blur-3xl opacity-10"></div>
        <div className="absolute bottom-40 -left-20 w-80 h-80 bg-gray-700 rounded-full filter blur-3xl opacity-10"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 relative z-10">
        {/* Encabezado de sección */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="inline-flex items-center bg-white/10 backdrop-blur-sm px-4 py-1 rounded-full text-sm font-medium mb-6"
          >
            <Zap className="w-4 h-4 mr-2 text-white" />
            <span className="text-white">PLANES FLEXIBLES</span>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-4xl md:text-5xl font-bold text-white mb-6"
          >
            Elige el plan perfecto para
            <span className="text-white"> tu estudio</span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto mb-8"
          >
            Planes diseñados específicamente para tatuadores, con todas las herramientas que necesitas
          </motion.p>

          {/* Toggle para cambiar entre planes mensuales y anuales */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex justify-center items-center gap-4 mb-8"
          >
            <span className={`text-sm font-medium ${!isAnnual ? 'text-white' : 'text-gray-400'}`}>Mensual</span>
            <button
              onClick={() => setIsAnnual(!isAnnual)}
              className="relative inline-flex h-6 w-12 items-center rounded-full bg-white/10 backdrop-blur-sm"
            >
              <span className="sr-only">Cambiar a plan anual</span>
              <span
                className={`${
                  isAnnual ? 'translate-x-6 bg-white' : 'translate-x-1 bg-gray-400'
                } inline-block h-4 w-4 transform rounded-full transition-transform duration-300`}
              />
            </button>
            <span className={`text-sm font-medium ${isAnnual ? 'text-white' : 'text-gray-400'}`}>
              Anual <span className="text-xs bg-white/20 text-white px-2 py-1 rounded-full ml-1">20% dto.</span>
            </span>
          </motion.div>
        </div>

        {/* Tarjetas de precios */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl mx-auto mb-16">
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7, delay: index * 0.2 }}
              className="relative"
            >
              {/* Tarjeta principal */}
              <div className={`relative bg-gray-900/50 backdrop-blur-sm rounded-2xl p-8 border ${
                plan.popular
                  ? 'border-white/30 shadow-lg'
                  : 'border-gray-800'
              }`}>
                {/* Badge de popular */}
                {plan.popular && (
                  <div className="absolute -top-4 right-6">
                    <div className="bg-white text-black px-4 py-1 rounded-full text-sm font-medium shadow-lg flex items-center">
                      <Star className="w-3 h-3 mr-1" fill="currentColor" />
                      Más Popular
                    </div>
                  </div>
                )}

                {/* Encabezado del plan */}
                <div className="mb-8">
                  <h3 className="text-2xl font-bold text-white mb-3">{plan.name}</h3>
                  <div className="relative mb-6">
                    {plan.isStudio ? (
                      <div>
                        {/* Selector de número de cuentas */}
                        <div className="flex items-center justify-between mb-4 bg-gray-800/50 rounded-lg p-3">
                          <span className="text-gray-300">Número de cuentas:</span>
                          <div className="flex items-center">
                            <button
                              onClick={() => setAccountCount(Math.max(2, accountCount - 1))}
                              className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-700 hover:bg-gray-600 transition-colors"
                              aria-label="Disminuir cuentas"
                            >
                              <MinusCircle className="w-4 h-4 text-white" />
                            </button>
                            <span className="mx-3 text-white font-bold">{accountCount}</span>
                            <button
                              onClick={() => setAccountCount(accountCount + 1)}
                              className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-700 hover:bg-gray-600 transition-colors"
                              aria-label="Aumentar cuentas"
                            >
                              <PlusCircle className="w-4 h-4 text-white" />
                            </button>
                          </div>
                        </div>

                        {/* Cálculo de precio */}
                        <div className="bg-gray-800/50 rounded-lg p-4 mb-4">
                          <div className="flex justify-between mb-2">
                            <span className="text-gray-400">Precio base por cuenta:</span>
                            <span className="text-gray-300">${isAnnual ? '40' : '50'}/mes</span>
                          </div>
                          <div className="flex justify-between mb-2">
                            <span className="text-gray-400">Descuento por cuenta:</span>
                            <span className="text-green-400">
                              {getDiscountPercentage(accountCount)}%
                            </span>
                          </div>
                          <div className="flex justify-between mb-2">
                            <span className="text-gray-400">Precio con descuento:</span>
                            <span className="text-white">${formatPrice(pricePerAccount)}/mes</span>
                          </div>
                          <div className="flex justify-between mb-2">
                            <span className="text-gray-400">Número de cuentas:</span>
                            <span className="text-white">{accountCount}</span>
                          </div>
                          <div className="flex justify-between border-t border-gray-700 pt-2 mt-2">
                            <span className="text-gray-300 font-medium">Total mensual:</span>
                            <div className="text-right">
                              <div className="text-white font-bold">
                                ${isAnnual ? plan.annualPrice : plan.price}/mes
                              </div>
                              {isAnnual && (
                                <div className="text-gray-500 text-xs">
                                  Pago anual de ${formatPrice(calculateStudioPrice(baseAnnualPrice, accountCount))}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-baseline">
                        <span className="text-5xl font-bold text-white">
                          ${isAnnual ? plan.annualPrice : plan.price}
                        </span>
                        {plan.price !== '0' && (
                          <span className="text-gray-400 ml-2 text-xl">/{isAnnual ? 'año' : 'mes'}</span>
                        )}
                        {plan.price !== '0' && isAnnual && (
                          <div className="text-gray-500 text-sm mt-1 ml-2">
                            Antes $600 (-20%)
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                  <p className="text-gray-300">{plan.description}</p>
                </div>

                {/* Características destacadas */}
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
                  {plan.highlightFeatures.map((feature, i) => {
                    const Icon = feature.icon;
                    return (
                      <div key={i} className="flex flex-col items-center text-center p-3 rounded-xl bg-gray-800/50">
                        <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center mb-2">
                          <Icon className="w-5 h-5 text-black" />
                        </div>
                        <span className="text-sm text-gray-300">{feature.name}</span>
                      </div>
                    );
                  })}
                </div>

                {/* Botón CTA */}
                {plan.cta === 'Contactar para Cotizar' ? (
                  <button
                    onClick={() => setIsFormOpen(true)}
                    className={`w-full py-4 px-6 rounded-full font-medium text-lg transition-all duration-300 inline-block text-center ${
                      plan.popular
                        ? 'bg-white text-black hover:bg-gray-100 hover:shadow-lg transform hover:-translate-y-1'
                        : 'bg-gray-800 text-white hover:bg-gray-700'
                    }`}
                  >
                    {plan.cta}
                  </button>
                ) : (
                  <Link
                    href={plan.url}
                    className={`w-full py-4 px-6 rounded-full font-medium text-lg transition-all duration-300 inline-block text-center ${
                      plan.popular
                        ? 'bg-white text-black hover:bg-gray-100 hover:shadow-lg transform hover:-translate-y-1'
                        : 'bg-gray-800 text-white hover:bg-gray-700'
                    }`}
                  >
                    {plan.cta}
                  </Link>
                )}

                {/* Texto de prueba gratis */}
                {plan.popular && (
                  <p className="text-center text-sm text-gray-400 mt-3">
                    1 mes gratis al comenzar el plan.
                  </p>
                )}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Tabla comparativa de características */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7 }}
          className="bg-gray-900/30 backdrop-blur-sm rounded-2xl border border-gray-800 overflow-hidden max-w-7xl mx-auto"
        >
          <div className="p-6 border-b border-gray-800">
            <h3 className="text-xl font-bold text-white">Comparación de características</h3>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-800">
                  <th className="py-4 px-6 text-left text-gray-300 font-medium">Característica</th>
                  {plans.map((plan, i) => (
                    <th key={i} className="py-4 px-6 text-center">
                      <span className="font-bold text-white">
                        {plan.name}
                      </span>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {plans[0].features.map((feature, i) => (
                  <tr key={i} className="border-b border-gray-800/50">
                    <td className="py-4 px-6 text-gray-300">
                      <div>
                        <div className="font-medium">{feature.name}</div>
                        <div className="text-sm text-gray-500">{feature.description}</div>
                      </div>
                    </td>
                    {plans.map((plan, planIndex) => (
                      <td key={planIndex} className="py-4 px-6 text-center">
                        {plan.features[i].included ? (
                          <div className="flex justify-center">
                            <div className="w-6 h-6 rounded-full bg-white flex items-center justify-center">
                              <Check className="w-4 h-4 text-black" />
                            </div>
                          </div>
                        ) : (
                          <div className="flex justify-center">
                            <div className="w-6 h-6 rounded-full bg-gray-800 flex items-center justify-center">
                              <X className="w-4 h-4 text-gray-600" />
                            </div>
                          </div>
                        )}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </motion.div>

        {/* Garantía y soporte */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mt-16 max-w-3xl mx-auto"
        >
          <div className="flex justify-center mb-4">
            <div className="w-12 h-12 rounded-full bg-gray-800 flex items-center justify-center">
              <Shield className="w-6 h-6 text-white" />
            </div>
          </div>

          <h3 className="text-xl font-bold text-white mb-2">Satisfacción garantizada</h3>
        </motion.div>
      </div>
    </section>
  );
}