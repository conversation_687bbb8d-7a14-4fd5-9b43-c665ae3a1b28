{"version": 2, "buildCommand": "npm run build:vercel && npm run post-build", "devCommand": "npm run dev", "installCommand": "npm install --legacy-peer-deps", "regions": ["iad1"], "outputDirectory": ".next", "framework": "nextjs", "rewrites": [{"source": "/:path((?!api|_next|static|public|app|home|login|register|studio|settings|dashboard|clients|analytics|calendar|finances|links|customization|automations|reviews|appointments|forgot-password|about|pricing|contact|terms|privacy|faq|features|blog|site-preview|messages|messages-new|portfolio|exito|fallido|notifications|profile|onboarding|assets|media).*)", "has": [{"type": "header", "key": "accept", "value": "text/html.*"}], "destination": "/[username]?username=:path"}], "headers": [{"source": "/", "headers": [{"key": "X-Robots-Tag", "value": "index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1"}]}, {"source": "/(politicas-de-privacidad|terminos-de-servicio|politica-de-reembolso)", "headers": [{"key": "X-Robots-Tag", "value": "index, follow"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,OPTIONS,PATCH,DELETE,POST,PUT"}, {"key": "Access-Control-Allow-Headers", "value": "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version"}, {"key": "X-Robots-Tag", "value": "noindex, nofollow, noarchive, nosnippet, nocache"}]}, {"source": "/(dashboard|clients|appointments|calendar|studio|finances|messages|settings|profile|automations|analytics|links|customization|portfolio|notifications|reviews|onboarding|site|insights|files)", "headers": [{"key": "X-Robots-Tag", "value": "noindex, nofollow, noarchive, nosnippet, nocache"}]}, {"source": "/(exito|fallido)", "headers": [{"key": "X-Robots-Tag", "value": "noindex, nofollow"}]}, {"source": "/([^/]+)$", "headers": [{"key": "X-Robots-Tag", "value": "noindex, nofollow, noarchive, nosnippet, nocache"}]}], "redirects": [{"source": "/www.tatu.ink/(.*)", "destination": "https://tatu.ink/$1", "permanent": true}], "crons": [{"path": "/api/cron/check-unipile-accounts", "schedule": "59 23 * * *"}], "env": {"NEXT_DISABLE_WEBPACK_WASM": "1", "NEXT_DISABLE_STATIC_GENERATION": "true", "DISABLE_STATIC_EXPORT": "true", "NEXT_DISABLE_FONT_OPTIMIZATION": "1", "NEXT_FONT_GOOGLE_INTER_FALLBACK": "system-ui, sans-serif", "NODE_OPTIONS": "--max-old-space-size=4096 --no-warnings", "NODE_ENV": "production", "NEXT_PRIVATE_PREBUNDLED_REACT": "next", "NEXT_TELEMETRY_DISABLED": "1"}, "build": {"env": {"NEXT_DISABLE_WEBPACK_WASM": "1", "NEXT_DISABLE_STATIC_GENERATION": "true", "DISABLE_STATIC_EXPORT": "true", "NEXT_DISABLE_FONT_OPTIMIZATION": "1", "NEXT_FONT_GOOGLE_INTER_FALLBACK": "system-ui, sans-serif", "NODE_OPTIONS": "--max-old-space-size=4096 --no-warnings", "NODE_ENV": "production", "NEXT_PRIVATE_PREBUNDLED_REACT": "next", "NEXT_FORCE_SERVER_SIDE_RENDERING": "true", "NEXT_PRIVATE_STANDALONE": "true", "NEXT_PUBLIC_DISABLE_OPTIMIZATION": "1"}}}