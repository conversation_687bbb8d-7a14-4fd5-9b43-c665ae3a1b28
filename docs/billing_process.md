---
name: Billing & Subscription Flow
created: 2025-07-02
---

# Billing & Subscription Flow (PayPal + Reveniu)

This document explains **how the complete billing system works** in the Tatu.ink code-base, starting the moment a user clicks **“Suscribirse / Cambiar plan”** up to the moment we receive and process provider **webhooks**.

---
## 1. Key folders & files

| Layer | Path | Purpose |
|-------|------|---------|
| **Client UI** | `app/components/settings/BillingSection.tsx` | Renders plans, shows “Suscribirse” button, handles cancel / reactivate, opens PayPal/Reveniu checkout. |
| | `app/settings/page.tsx` | Settings page that embeds the `BillingSection` component. |
| | `app/components/layout/Sidebar.tsx` | Navigation link to `/settings` so the user can reach Billing. |
| **API routes** | `app/api/paypal/subscribe/route.ts` | Creates PayPal subscription & returns `approval_url`. |
| | `app/api/reveniu/subscribe/route.ts` | Creates Reveniu subscription (same spirit). |
| | `app/api/paypal/*` | Additional helpers: `activate`, `cancel`, `reactivate`, `suspend`, `sync`, etc. |
| | `app/api/reveniu/*` | Mirror helpers for Reveniu ( cancel / reactivate / plans / … ). |
| **Webhooks** | `app/api/paypal/webhook/route.ts` | Receives **PayPal** webhook events ( >10 event types handled). Updates `users`, `subscriptions`, `subscription_mappings`, etc. |
| | `app/api/reveniu/webhook/route.ts` | Receives **Reveniu** webhooks (`subscription_renewal_cancelled`, `subscription_deactivated`). |
| **Domain logic** | `lib/paypal/*` | SDK wrappers + signature verification. |
| | `lib/reveniu/*` | Reveniu config, helper functions. |
| | `lib/services/payment.ts` | Central service that writes/updates `payments` & `subscriptions` collections. |
| **Firebase Admin** | `lib/firebase-admin.ts` (imported inside webhooks) | Gives privileged DB access for server-side mutations. |

Environment variables such as `PAYPAL_MONTHLY_PLAN_ID`, `REVENIU_SECRET_KEY` are consumed inside the API routes.

---
## 2. High level sequence

```mermaid
sequenceDiagram
participant C as Client browser
participant FE as BillingSection (Next.js client)
participant API as /api/* routes (Next.js server actions)
participant PP as PayPal / Reveniu
participant WH as Webhook endpoint
participant DB as Firestore

Note over FE,API: Same repo – just client/server boundaries

C->>FE: Click "Suscribirse"
FE->>API: POST /api/{provider}/subscribe {user, planId, billingCycle}
API->>PP: Create subscription via SDK/HTTP
PP-->>API: subscription {id, links[]}
API-->>FE: {approval_url}
FE-->>C: Open approval_url (new window)
C->>PP: Approve payment (provider UI)
PP-->>WH: POST webhook event (e.g. SUBSCRIPTION.ACTIVATED)
WH->>DB: Update users, subscriptions, mappings, history, tasks
WH-->>PP: 200 OK
DB-->>FE: onSnapshot() triggers SettingsContext refresh
```

---
## 3. Detailed flow – PayPal

1. **Button click** in `BillingSection.tsx` executes `fetch('/api/paypal/subscribe')` with `{ user, billingCycle, planType }`.
2. `app/api/paypal/subscribe/route.ts` chooses the correct **PayPal Plan ID** (monthly / yearly / promo) using env vars and builds a `PayPalSubscriptionRequest`.
3. Utility `createSubscription` (in `lib/paypal/utils.ts`) calls the PayPal REST API `v1/billing/subscriptions`.
4. PayPal responds with `subscription.id` and an **approval link** – returned to the client.
5. The browser redirects the user to `approval_url`; after approval PayPal redirects back to `https://app.tatu.ink/exito` (or `fallido`).
6. Independently, PayPal sends a **webhook** (configured in the PayPal dashboard) to
   `POST /api/paypal/webhook`.
   * File `app/api/paypal/webhook/route.ts` first verifies the signature via `verifyWebhookSignature`.
   * Switches over `event_type`, e.g. `BILLING.SUBSCRIPTION.ACTIVATED`, `UPDATED`, `SUSPENDED`, etc.
   * Uses Firebase Admin (`db` instance) to write:
     * `users/{uid}` – flags like `subscriptionStatus`, `subscriptionEndDate`, …
     * `subscriptions/{uid}` – canonical subscription data.
     * `subscription_mappings` – mapping between PayPal `subscription.id` and our `uid`.
     * `subscription_history` – audit log.
     * `scheduled_tasks` – deferred downgrade jobs (e.g., move to "free" after next billing cycle).
7. Changes are listened to in the client via `AuthContext` / `SettingsContext` → UI updates automatically.

---
## 4. Detailed flow – Reveniu

The Reveniu path mirrors PayPal with minor differences:

1. Client calls `POST /api/reveniu/subscribe` (logic very similar, plan IDs are fixed inside `app/api/reveniu/plans/route.ts`).
2. Server uses Reveniu REST API (`https://production.reveniu.com/api/v1/...`) with secret key from `lib/reveniu/config.ts` to create the subscription and returns a **checkout URL**.
3. After payment completion Reveniu triggers webhooks to `POST /api/reveniu/webhook`.
4. `app/api/reveniu/webhook/route.ts` handles events:
   * `subscription_renewal_cancelled` – marks future cancellation but keeps access until `next_due`.
   * `subscription_deactivated` – schedules downgrade.
5. Firestore updates follow the same pattern as PayPal (collections, scheduled tasks, history, etc.).

---
## 5. Collections used in Firestore

| Collection | Purpose |
|------------|---------|
| `users` | Per-user primary doc; stores `subscriptionStatus`, `plan`, `finalAccessDate`, etc. |
| `subscriptions` | Detailed subscription object (status, provider, period dates). Key = `uid`. |
| `payments` | Individual payment attempts/records. |
| `subscription_mappings` | `{ providerSubscriptionId, provider (paypal|reveniu), userId }` link. |
| `subscription_history` | Immutable audit log. |
| `scheduled_tasks` | Deferred jobs (Cloud Functions / CRON) to auto-downgrade after `finalAccessDate`. |

---
## 6. Changing or extending the system

* **Add a new provider**: replicate folder structure under `app/api/{provider}` with `subscribe` & `webhook` routes and plug into `BillingSection` button logic.
* **Testing webhooks locally**: use tools such as ngrok to expose `localhost:3000/api/paypal/webhook` and add the public URL in the provider dashboard.
* **Important env vars** (defined in Vercel/Netlify):
  * PayPal: `PAYPAL_CLIENT_ID`, `PAYPAL_CLIENT_SECRET`, `PAYPAL_MONTHLY_PLAN_ID`, ...
  * Reveniu: `REVENU_SECRET_KEY`, optional custom plan IDs.

---
## 7. Quick trace reference

| Step | File | Function / Code | Notes |
|------|------|-----------------|-------|
| 1 | `app/components/settings/BillingSection.tsx` | `handleSubscribeClick` (inline) | Calls `/api/{provider}/subscribe`. |
| 2 | `app/api/paypal/subscribe/route.ts` | `POST` | Creates subscription. |
| 3 | Provider UI | — | User approves payment. |
| 4 | `app/api/paypal/webhook/route.ts` | `POST` | Processes activation / updates. |
| 5 | `lib/services/payment.ts` | `PaymentService.updateSubscription` | Persists in Firestore. |
| 6 | Client Contexts | `AuthContext`, `SettingsContext` | onSnapshot() reflects changes. |

---
### End of document
