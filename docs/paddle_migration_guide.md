# Guía de Migración a Paddle

## Resumen de Cambios Implementados

### 1. Archivos Creados

#### Configuración y Tipos
- `lib/paddle/config.ts` - Configuración de Paddle con URLs de checkout
- `lib/paddle/types.ts` - Tipos TypeScript para API de Paddle
- `lib/paddle/utils.ts` - Utilidades para interactuar con Paddle API

#### API Routes
- `app/api/paddle/subscribe/route.ts` - Genera URLs de checkout de Paddle
- `app/api/paddle/webhook/route.ts` - Procesa webhooks de Paddle
- `app/api/paddle/customer-portal/route.ts` - Crea sesiones de customer portal

### 2. Archivos Modificados

#### Servicios
- `lib/services/payment.ts` - Agregados métodos específicos para Paddle

#### Componentes
- `app/components/settings/BillingSection.tsx` - Actualizado para usar Paddle

## Variables de Entorno Requeridas

Agrega estas variables a tu archivo `.env.local`:

```bash
# Paddle Configuration
PADDLE_API_KEY=your_paddle_api_key_here
PADDLE_SANDBOX=false  # true para sandbox, false para producción
```

## URLs de Checkout de Paddle

Las URLs están configuradas en `lib/paddle/config.ts`:

- **Mensual**: `https://pay.paddle.io/hsc_01jypp2mn7q86qmr1a1qmgbnrq_2jedg02th2hwy2r3apg661spag5jkjnh`
- **Anual**: `https://pay.paddle.io/hsc_01jz1jhbs3dn1p4nd8rrqw0z6g_9gc65ak5b07ygbk7azwrb1egrjxzzb6p`

## Flujo de Suscripción con Paddle

### 1. Proceso de Checkout
1. Usuario hace clic en "Comenzar Ahora"
2. Se abre nueva ventana con URL de Paddle + email del usuario
3. Usuario completa pago en Paddle
4. Paddle envía webhook a `/api/paddle/webhook`

### 2. Procesamiento de Webhooks
Los siguientes eventos son procesados con lógica específica de acceso:

#### `subscription.activated`
- ✅ **Acceso**: Inmediato y completo
- 📊 **Estado**: `active`
- 🔄 **Acción**: Usuario obtiene todos los beneficios

#### `subscription.trialing`
- 🆓 **Acceso**: Inmediato y completo (período de prueba)
- 📊 **Estado**: `active`
- 🔄 **Acción**: Usuario obtiene todos los beneficios durante el trial

#### `subscription.canceled`
- ❌ **Acceso**: Se quita inmediatamente
- 📊 **Estado**: `inactive`
- 🔄 **Acción**: Paddle ya manejó el período de gracia

#### `subscription.paused`
- ⏸️ **Acceso**: Mantiene hasta `current_billing_period.ends_at`
- 📊 **Estado**: `active` hasta fin de período, luego `inactive`
- 🔄 **Acción**: Programa tarea para quitar acceso al final del período

#### `subscription.past_due`
- ⚠️ **Acceso**: Mantiene hasta `current_billing_period.ends_at`
- 📊 **Estado**: `active` hasta fin de período, luego `inactive`
- 🔄 **Acción**: Programa tarea para verificar acceso al final del período

#### `subscription.resumed`
- ▶️ **Acceso**: Restaura inmediatamente
- 📊 **Estado**: `active`
- 🔄 **Acción**: Cancela tareas programadas de quitar acceso

#### `subscription.updated`
- 🔄 **Acceso**: Mantiene estado actual
- 📊 **Estado**: Según el nuevo status
- 🔄 **Acción**: Actualiza información sin cambiar acceso

### 3. Gestión de Suscripciones
1. Usuario hace clic en "Gestionar Suscripción"
2. Se llama a `/api/paddle/customer-portal`
3. Se crea sesión de customer portal en Paddle
4. Se abre portal de Paddle en nueva ventana

## Estructura de Datos en Firestore

### Colección `users`
```javascript
{
  subscriptionStatus: 'active' | 'inactive', // Estado de acceso calculado
  subscriptionProvider: 'paddle',
  paddleCustomerId: 'ctm_xxx',
  paddleSubscriptionId: 'sub_xxx',
  subscriptionPlanId: 'pri_xxx',
  nextBillingDate: Date,
  subscriptionEndDate: Date,
  subscriptionCancelled: boolean,
  subscriptionSuspended: boolean,
  subscriptionPaused: boolean, // Nuevo: específico para pausas
  subscriptionPastDue: boolean, // Nuevo: específico para pagos vencidos
  paddleStatus: string, // Nuevo: estado original de Paddle
  accessReason: string, // Nuevo: razón del estado de acceso
  finalAccessDate: Date, // Nuevo: fecha hasta cuando tiene acceso
  updatedAt: Date
}
```

### Colección `subscriptions`
```javascript
{
  userId: string,
  subscriptionId: string,
  status: string,
  provider: 'paddle',
  customerId: string,
  planId: string,
  billingCycle: { interval: string, frequency: number },
  currentPeriodStart: Date,
  currentPeriodEnd: Date,
  nextBillingDate: Date,
  cancelAtPeriodEnd: boolean
}
```

### Colección `subscription_mappings`
```javascript
{
  userId: string,
  provider: 'paddle',
  providerSubscriptionId: string,
  paddleCustomerId: string
}
```

### Colección `scheduled_tasks` (Nueva)
```javascript
{
  userId: string,
  type: 'check_paused_access' | 'check_past_due_access',
  scheduledFor: Date, // Fecha cuando ejecutar la tarea
  data: {
    subscriptionId: string,
    provider: 'paddle',
    reason: string
  },
  status: 'pending' | 'completed' | 'cancelled',
  createdAt: Date,
  cancelledAt?: Date // Si fue cancelada
}
```

## Configuración de Webhooks en Paddle

1. Ve al dashboard de Paddle
2. Configura webhook endpoint: `https://app.tatu.ink/api/paddle/webhook`
3. Selecciona los siguientes eventos:
   - subscription.activated
   - subscription.canceled
   - subscription.updated
   - subscription.past_due
   - subscription.paused
   - subscription.resumed

## Testing

### 1. Verificar Variables de Entorno
```bash
# Verificar que las variables están configuradas
echo $PADDLE_API_KEY
```

### 2. Probar Checkout
1. Ir a la página de billing
2. Hacer clic en "Comenzar Ahora"
3. Verificar que se abre Paddle con el email correcto

### 3. Probar Customer Portal
1. Con una suscripción activa, hacer clic en "Gestionar Suscripción"
2. Verificar que se abre el portal de Paddle

### 4. Probar Webhooks
1. Usar herramientas como ngrok para testing local
2. Configurar webhook en Paddle dashboard
3. Realizar una transacción de prueba
4. Verificar logs en `/api/paddle/webhook`

## Migración de Datos Existentes

Para usuarios existentes con suscripciones de PayPal/Reveniu:
1. Los datos existentes se mantienen
2. Nuevas suscripciones usarán Paddle
3. Usuarios existentes pueden renovar con Paddle cuando expire su suscripción actual

## Rollback Plan

Si necesitas volver al sistema anterior:
1. Cambiar los botones en `BillingSection.tsx` para usar las URLs anteriores
2. Deshabilitar el webhook de Paddle
3. Reactivar webhooks de PayPal/Reveniu

## Monitoreo

Verificar logs en:
- `/api/paddle/webhook` - Para eventos de Paddle
- `/api/paddle/customer-portal` - Para creación de portales
- Console del navegador - Para errores de frontend
