import { adminDb } from '../firebase/admin';
import type { Payment, Subscription } from '../firebase/types';
import type { FirebaseError } from 'firebase-admin';

interface PlanDetails {
  id: string;
  name: string;
  amount: number;
  currency: string;
  interval: 'month' | 'year';
}

interface PaymentMethod {
  type: string;
  last4?: string;
  brand?: string;
}

export class PaymentService {
  static async createPayment(data: Omit<Payment, 'id'>): Promise<string> {
    try {
      console.log('Creating payment document with data:', {
        ...data,
        userId: data.userId
      });

      // Asegurar que la colección payments existe
      const paymentsCollection = adminDb.collection('payments');
      
      // Crear el documento de pago usando admin SDK
      const paymentDoc = {
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      console.log('Attempting to create payment document:', paymentDoc);
      const paymentRef = await paymentsCollection.add(paymentDoc);
      console.log('Payment document created with ID:', paymentRef.id);

      // Asegurar que la colección subscriptions existe
      const subscriptionsCollection = adminDb.collection('subscriptions');
      const subscriptionRef = subscriptionsCollection.doc(data.userId);
      
      try {
        const subscriptionDoc = {
          userId: data.userId,
          plan: 'free' as const,
          status: 'pending' as const,
          createdAt: new Date(),
          updatedAt: new Date(),
          currentPeriodStart: null,
          currentPeriodEnd: null,
          cancelAtPeriodEnd: false
        };
        
        console.log('Attempting to create/update subscription document:', subscriptionDoc);
        await subscriptionRef.set(subscriptionDoc, { merge: true });
        console.log('Subscription document created/updated successfully');
      } catch (subscriptionError) {
        const err = subscriptionError as FirebaseError;
        console.error('Error creating/updating subscription:', {
          error: err,
          errorMessage: err.message,
          errorCode: err.code,
          stack: err.stack
        });
      }

      return paymentRef.id;
    } catch (error) {
      const err = error as FirebaseError;
      console.error('Detailed error in createPayment:', {
        error: err,
        errorMessage: err.message,
        errorCode: err.code,
        stack: err.stack
      });
      throw error;
    }
  }

  static async updatePaymentStatus(
    paymentId: string, 
    status: Payment['status'],
    reveniuPaymentId?: string
  ): Promise<void> {
    try {
      console.log('Updating payment status:', { paymentId, status, reveniuPaymentId });
      const paymentRef = adminDb.collection('payments').doc(paymentId);
      
      const updateData = {
        status,
        paymentId: reveniuPaymentId,
        updatedAt: new Date()
      };
      
      console.log('Attempting to update payment with data:', updateData);
      await paymentRef.update(updateData);
      console.log('Payment status updated successfully');
    } catch (error) {
      const err = error as FirebaseError;
      console.error('Error updating payment status:', {
        error: err,
        errorMessage: err.message,
        errorCode: err.code,
        stack: err.stack
      });
      throw error;
    }
  }

  static async updateSubscription(
    userId: string,
    data: Partial<Subscription> & {
      planDetails?: PlanDetails | null;
      paymentMethod?: PaymentMethod | null;
    }
  ): Promise<void> {
    try {
      console.log('Updating subscription:', { userId, data });
      const subscriptionRef = adminDb.collection('subscriptions').doc(userId);

      const { planDetails, paymentMethod, ...subscriptionData } = data;

      const updateData = {
        ...subscriptionData,
        updatedAt: new Date(),
        ...(planDetails && {
          planDetails: {
            id: planDetails.id,
            name: planDetails.name,
            amount: planDetails.amount,
            currency: planDetails.currency,
            interval: planDetails.interval
          }
        }),
        ...(paymentMethod && {
          paymentMethod: {
            type: paymentMethod.type,
            last4: paymentMethod.last4,
            brand: paymentMethod.brand
          }
        })
      };

      console.log('Attempting to update subscription with data:', updateData);
      await subscriptionRef.set(updateData, { merge: true });
      console.log('Subscription updated successfully');
    } catch (error) {
      const err = error as FirebaseError;
      console.error('Error updating subscription:', {
        error: err,
        errorMessage: err.message,
        errorCode: err.code,
        stack: err.stack
      });
      throw error;
    }
  }

  // Métodos específicos para Paddle
  static async createPaddleSubscription(
    userId: string,
    subscriptionData: {
      subscriptionId: string;
      customerId: string;
      status: string;
      planId: string;
      billingCycle: { interval: string; frequency: number };
      currentPeriodStart?: Date;
      currentPeriodEnd?: Date;
      nextBillingDate?: Date;
    }
  ): Promise<void> {
    try {
      console.log('Creating Paddle subscription:', { userId, subscriptionId: subscriptionData.subscriptionId });

      const batch = adminDb.batch();

      // Actualizar documento de usuario
      const userRef = adminDb.collection('users').doc(userId);
      const userUpdateData = {
        subscriptionStatus: subscriptionData.status === 'active' ? 'active' : 'inactive',
        subscriptionProvider: 'paddle',
        paddleCustomerId: subscriptionData.customerId,
        paddleSubscriptionId: subscriptionData.subscriptionId,
        subscriptionPlanId: subscriptionData.planId,
        nextBillingDate: subscriptionData.nextBillingDate || null,
        subscriptionEndDate: subscriptionData.currentPeriodEnd || null,
        subscriptionCancelled: subscriptionData.status === 'canceled',
        subscriptionSuspended: subscriptionData.status === 'past_due' || subscriptionData.status === 'paused',
        updatedAt: new Date()
      };

      batch.set(userRef, userUpdateData, { merge: true });

      // Crear documento de suscripción
      const subscriptionRef = adminDb.collection('subscriptions').doc(userId);
      const subscriptionDoc = {
        userId: userId,
        subscriptionId: subscriptionData.subscriptionId,
        status: subscriptionData.status,
        provider: 'paddle',
        customerId: subscriptionData.customerId,
        planId: subscriptionData.planId,
        billingCycle: subscriptionData.billingCycle,
        currentPeriodStart: subscriptionData.currentPeriodStart || null,
        currentPeriodEnd: subscriptionData.currentPeriodEnd || null,
        nextBillingDate: subscriptionData.nextBillingDate || null,
        cancelAtPeriodEnd: subscriptionData.status === 'canceled',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      batch.set(subscriptionRef, subscriptionDoc, { merge: true });

      // Crear mapping
      const mappingRef = adminDb.collection('subscription_mappings').doc(subscriptionData.subscriptionId);
      const mappingData = {
        userId: userId,
        provider: 'paddle',
        providerSubscriptionId: subscriptionData.subscriptionId,
        paddleCustomerId: subscriptionData.customerId,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      batch.set(mappingRef, mappingData);

      // Ejecutar batch
      await batch.commit();
      console.log('Paddle subscription created successfully');

    } catch (error) {
      const err = error as FirebaseError;
      console.error('Error creating Paddle subscription:', {
        error: err,
        errorMessage: err.message,
        errorCode: err.code,
        stack: err.stack
      });
      throw error;
    }
  }

  static async updatePaddleSubscription(
    userId: string,
    subscriptionData: {
      subscriptionId: string;
      customerId: string;
      status: string;
      planId?: string;
      billingCycle?: { interval: string; frequency: number };
      currentPeriodStart?: Date;
      currentPeriodEnd?: Date;
      nextBillingDate?: Date;
      canceledAt?: Date;
    }
  ): Promise<void> {
    try {
      console.log('Updating Paddle subscription:', { userId, subscriptionId: subscriptionData.subscriptionId });

      const batch = adminDb.batch();

      // Actualizar documento de usuario
      const userRef = adminDb.collection('users').doc(userId);
      const userUpdateData: any = {
        subscriptionStatus: subscriptionData.status === 'active' ? 'active' : 'inactive',
        subscriptionProvider: 'paddle',
        paddleCustomerId: subscriptionData.customerId,
        paddleSubscriptionId: subscriptionData.subscriptionId,
        subscriptionCancelled: subscriptionData.status === 'canceled',
        subscriptionSuspended: subscriptionData.status === 'past_due' || subscriptionData.status === 'paused',
        updatedAt: new Date()
      };

      if (subscriptionData.planId) {
        userUpdateData.subscriptionPlanId = subscriptionData.planId;
      }

      if (subscriptionData.nextBillingDate) {
        userUpdateData.nextBillingDate = subscriptionData.nextBillingDate;
      }

      if (subscriptionData.currentPeriodEnd) {
        userUpdateData.subscriptionEndDate = subscriptionData.currentPeriodEnd;
      }

      if (subscriptionData.canceledAt) {
        userUpdateData.finalAccessDate = subscriptionData.currentPeriodEnd || subscriptionData.canceledAt;
      }

      batch.update(userRef, userUpdateData);

      // Actualizar documento de suscripción
      const subscriptionRef = adminDb.collection('subscriptions').doc(userId);
      const subscriptionUpdateData: any = {
        status: subscriptionData.status,
        cancelAtPeriodEnd: subscriptionData.status === 'canceled',
        updatedAt: new Date()
      };

      if (subscriptionData.planId) {
        subscriptionUpdateData.planId = subscriptionData.planId;
      }

      if (subscriptionData.billingCycle) {
        subscriptionUpdateData.billingCycle = subscriptionData.billingCycle;
      }

      if (subscriptionData.currentPeriodStart) {
        subscriptionUpdateData.currentPeriodStart = subscriptionData.currentPeriodStart;
      }

      if (subscriptionData.currentPeriodEnd) {
        subscriptionUpdateData.currentPeriodEnd = subscriptionData.currentPeriodEnd;
      }

      if (subscriptionData.nextBillingDate) {
        subscriptionUpdateData.nextBillingDate = subscriptionData.nextBillingDate;
      }

      batch.update(subscriptionRef, subscriptionUpdateData);

      // Ejecutar batch
      await batch.commit();
      console.log('Paddle subscription updated successfully');

    } catch (error) {
      const err = error as FirebaseError;
      console.error('Error updating Paddle subscription:', {
        error: err,
        errorMessage: err.message,
        errorCode: err.code,
        stack: err.stack
      });
      throw error;
    }
  }
}