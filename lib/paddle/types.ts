// Tipos para la API de Paddle

// Tipo para Customer de Paddle
export interface PaddleCustomer {
  id: string;
  status: 'active' | 'archived';
  custom_data?: any;
  name?: string;
  email: string;
  marketing_consent: boolean;
  locale: string;
  created_at: string;
  updated_at: string;
  import_meta?: any;
}

// Tipo para respuesta de Customer API
export interface PaddleCustomerResponse {
  data: PaddleCustomer;
  meta: {
    request_id: string;
  };
}

// Tipo para Subscription de Paddle
export interface PaddleSubscription {
  id: string;
  items: PaddleSubscriptionItem[];
  status: 'active' | 'canceled' | 'past_due' | 'paused' | 'trialing';
  discount?: any;
  paused_at?: string;
  address_id?: string;
  created_at: string;
  started_at: string;
  updated_at: string;
  business_id?: string;
  canceled_at?: string;
  custom_data?: any;
  customer_id: string;
  import_meta?: any;
  billing_cycle: {
    interval: 'day' | 'week' | 'month' | 'year';
    frequency: number;
  };
  currency_code: string;
  next_billed_at?: string;
  billing_details?: any;
  collection_mode: 'automatic' | 'manual';
  first_billed_at?: string;
  scheduled_change?: any;
  current_billing_period?: {
    ends_at: string;
    starts_at: string;
  };
}

// Tipo para items de suscripción
export interface PaddleSubscriptionItem {
  price: PaddlePrice;
  product: PaddleProduct;
  status: 'active' | 'inactive';
  quantity: number;
  recurring: boolean;
  created_at: string;
  updated_at: string;
  trial_dates?: any;
  next_billed_at?: string;
  previously_billed_at?: string;
}

// Tipo para Price de Paddle
export interface PaddlePrice {
  id: string;
  name: string;
  type: 'standard' | 'custom';
  status: 'active' | 'archived';
  quantity?: {
    maximum: number;
    minimum: number;
  };
  tax_mode: 'account_setting' | 'external';
  created_at: string;
  product_id: string;
  unit_price: {
    amount: string;
    currency_code: string;
  };
  updated_at: string;
  custom_data?: any;
  description?: string;
  import_meta?: any;
  trial_period?: any;
  billing_cycle?: {
    interval: 'day' | 'week' | 'month' | 'year';
    frequency: number;
  };
  unit_price_overrides?: any[];
}

// Tipo para Product de Paddle
export interface PaddleProduct {
  id: string;
  name: string;
  type: 'standard' | 'custom';
  tax_category: string;
  description?: string;
  image_url?: string;
  custom_data?: any;
  status: 'active' | 'archived';
  import_meta?: any;
  created_at: string;
  updated_at: string;
}

// Tipos para Webhooks de Paddle
export interface PaddleWebhookEvent {
  event_id: string;
  event_type: string;
  occurred_at: string;
  notification_id: string;
  data: PaddleSubscription | PaddleCustomer | any;
}

// Tipos específicos para eventos de webhook
export interface PaddleSubscriptionActivatedEvent extends PaddleWebhookEvent {
  event_type: 'subscription.activated';
  data: PaddleSubscription;
}

export interface PaddleSubscriptionCanceledEvent extends PaddleWebhookEvent {
  event_type: 'subscription.canceled';
  data: PaddleSubscription;
}

export interface PaddleSubscriptionUpdatedEvent extends PaddleWebhookEvent {
  event_type: 'subscription.updated';
  data: PaddleSubscription;
}

export interface PaddleSubscriptionPastDueEvent extends PaddleWebhookEvent {
  event_type: 'subscription.past_due';
  data: PaddleSubscription;
}

export interface PaddleSubscriptionPausedEvent extends PaddleWebhookEvent {
  event_type: 'subscription.paused';
  data: PaddleSubscription;
}

export interface PaddleSubscriptionResumedEvent extends PaddleWebhookEvent {
  event_type: 'subscription.resumed';
  data: PaddleSubscription;
}

// Tipo para Customer Portal Session
export interface PaddleCustomerPortalSession {
  id: string;
  customer_id: string;
  urls: {
    general: {
      overview: string;
    };
    subscriptions: Array<{
      id: string;
      cancel_subscription: string;
      update_subscription_payment_method: string;
    }>;
  };
  created_at: string;
}

// Tipo para respuesta de Customer Portal Session
export interface PaddleCustomerPortalResponse {
  data: PaddleCustomerPortalSession;
  meta: {
    request_id: string;
  };
}

// Tipo para crear Customer Portal Session
export interface CreateCustomerPortalRequest {
  subscription_ids: string[];
}

// Tipo para nuestra aplicación - mantener compatibilidad con el sistema actual
export interface PaddleSubscriptionForApp {
  id: string;
  status: {
    code: string;
    description: string;
    isActive: boolean;
  };
  interval: {
    code: string;
    description: string;
    isMensual: boolean;
    isAnual: boolean;
  };
  plan: {
    id: string;
    name: string;
    description: string;
    amount: number;
  };
  dates: {
    created: Date;
    nextPayment: Date;
  };
  customer: {
    id: string;
    email: string;
    name: string;
  };
  payment: {
    method: string;
    lastDigits: string | null;
    autoRenew: boolean;
  };
}
