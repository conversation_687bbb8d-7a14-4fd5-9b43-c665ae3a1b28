import { paddleConfig } from './config';
import { 
  PaddleCustomer, 
  PaddleCustomerResponse, 
  PaddleCustomerPortalResponse, 
  CreateCustomerPortalRequest,
  PaddleSubscription 
} from './types';

// Función para obtener headers de autenticación para Paddle
function getPaddleHeaders(): Record<string, string> {
  if (!paddleConfig.apiKey) {
    throw new Error('PADDLE_API_KEY no está configurada');
  }

  return {
    'Authorization': `Bearer ${paddleConfig.apiKey}`,
    'Content-Type': 'application/json',
  };
}

// Función para obtener un customer por ID
export async function getPaddleCustomer(customerId: string): Promise<PaddleCustomer> {
  try {
    console.log('🔍 Obteniendo customer de Paddle:', customerId);
    
    const response = await fetch(`${paddleConfig.baseUrl}/customers/${customerId}`, {
      method: 'GET',
      headers: getPaddleHeaders(),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Error al obtener customer de Paddle:', response.status, errorText);
      throw new Error(`Error al obtener customer: ${response.status} ${errorText}`);
    }

    const data: PaddleCustomerResponse = await response.json();
    console.log('✅ Customer obtenido:', data.data.email);
    
    return data.data;
  } catch (error) {
    console.error('❌ Error en getPaddleCustomer:', error);
    throw error;
  }
}

// Función para crear una sesión de customer portal
export async function createCustomerPortalSession(
  customerId: string, 
  subscriptionIds: string[]
): Promise<string> {
  try {
    console.log('🔗 Creando sesión de customer portal para:', customerId);
    console.log('📋 Subscription IDs:', subscriptionIds);
    
    const requestBody: CreateCustomerPortalRequest = {
      subscription_ids: subscriptionIds
    };

    const response = await fetch(`${paddleConfig.baseUrl}/customers/${customerId}/portal-sessions`, {
      method: 'POST',
      headers: getPaddleHeaders(),
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Error al crear customer portal session:', response.status, errorText);
      throw new Error(`Error al crear portal session: ${response.status} ${errorText}`);
    }

    const data: PaddleCustomerPortalResponse = await response.json();
    console.log('✅ Customer portal session creada');
    
    // Retornar la URL de overview del portal
    return data.data.urls.general.overview;
  } catch (error) {
    console.error('❌ Error en createCustomerPortalSession:', error);
    throw error;
  }
}

// Función para obtener una suscripción por ID
export async function getPaddleSubscription(subscriptionId: string): Promise<PaddleSubscription> {
  try {
    console.log('🔍 Obteniendo suscripción de Paddle:', subscriptionId);
    
    const response = await fetch(`${paddleConfig.baseUrl}/subscriptions/${subscriptionId}`, {
      method: 'GET',
      headers: getPaddleHeaders(),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Error al obtener suscripción de Paddle:', response.status, errorText);
      throw new Error(`Error al obtener suscripción: ${response.status} ${errorText}`);
    }

    const data = await response.json();
    console.log('✅ Suscripción obtenida:', data.data.id);
    
    return data.data;
  } catch (error) {
    console.error('❌ Error en getPaddleSubscription:', error);
    throw error;
  }
}

// Función para generar URL de checkout con email del usuario
export function generatePaddleCheckoutUrl(billingCycle: 'monthly' | 'yearly', userEmail: string): string {
  const baseUrl = paddleConfig.checkoutUrls[billingCycle];
  const params = new URLSearchParams({
    theme: paddleConfig.theme,
    user_email: userEmail
  });
  
  return `${baseUrl}?${params.toString()}`;
}

// Función para verificar webhook signature (si Paddle lo requiere)
export async function verifyPaddleWebhookSignature(
  body: string,
  signature: string
): Promise<boolean> {
  // Paddle usa un método diferente para verificar webhooks
  // Por ahora retornamos true, pero deberías implementar la verificación real
  // según la documentación de Paddle
  console.log('⚠️ Verificación de webhook signature no implementada para Paddle');
  return true;
}

// Función helper para mapear status de Paddle a nuestro sistema
export function mapPaddleStatusToApp(paddleStatus: string): {
  code: string;
  description: string;
  isActive: boolean;
} {
  switch (paddleStatus.toLowerCase()) {
    case 'active':
      return {
        code: 'active',
        description: 'Activa',
        isActive: true
      };
    case 'canceled':
      return {
        code: 'canceled',
        description: 'Cancelada',
        isActive: false
      };
    case 'past_due':
      return {
        code: 'past_due',
        description: 'Pago Vencido',
        isActive: false
      };
    case 'paused':
      return {
        code: 'paused',
        description: 'Pausada',
        isActive: false
      };
    case 'trialing':
      return {
        code: 'trialing',
        description: 'Período de Prueba',
        isActive: true
      };
    default:
      return {
        code: paddleStatus,
        description: paddleStatus,
        isActive: false
      };
  }
}

// Función helper para determinar el ciclo de facturación
export function mapPaddleBillingCycle(interval: string, frequency: number): {
  code: string;
  description: string;
  isMensual: boolean;
  isAnual: boolean;
} {
  if (interval === 'month' && frequency === 1) {
    return {
      code: 'monthly',
      description: 'Mensual',
      isMensual: true,
      isAnual: false
    };
  } else if (interval === 'year' && frequency === 1) {
    return {
      code: 'yearly',
      description: 'Anual',
      isMensual: false,
      isAnual: true
    };
  } else {
    return {
      code: `${interval}_${frequency}`,
      description: `Cada ${frequency} ${interval}(s)`,
      isMensual: false,
      isAnual: false
    };
  }
}
